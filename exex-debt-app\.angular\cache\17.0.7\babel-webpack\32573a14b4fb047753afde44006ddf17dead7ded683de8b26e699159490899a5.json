{"ast": null, "code": "import { catchError, switchMap } from 'rxjs/operators';\nimport { throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nexport class JwtRefreshInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(request, next) {\n    return next.handle(request).pipe(catchError(error => {\n      // Skip refresh logic for login, refresh, and logout endpoints\n      const isLoginEndpoint = request.url.includes('/api/auth/login');\n      const isRefreshEndpoint = request.url.includes('/api/auth/refresh');\n      const isLogoutEndpoint = request.url.includes('/api/auth/logout');\n      if (error.status === 401 && !isLoginEndpoint && !isRefreshEndpoint && !isLogoutEndpoint) {\n        // Token expired or unauthorized; attempt to refresh it\n        return this.authService.refreshToken().pipe(switchMap(response => {\n          // Retry the original request with the new token\n          const updatedRequest = request.clone({\n            setHeaders: {\n              Authorization: `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`\n            }\n          });\n          return next.handle(updatedRequest);\n        }), catchError(refreshError => {\n          // Refresh token failed; log out the user immediately\n          this.authService.logoutImmediate();\n          return throwError(() => refreshError);\n        }));\n      }\n      return throwError(() => error);\n    }));\n  }\n  static #_ = this.ɵfac = function JwtRefreshInterceptor_Factory(t) {\n    return new (t || JwtRefreshInterceptor)(i0.ɵɵinject(i1.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: JwtRefreshInterceptor,\n    factory: JwtRefreshInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["catchError", "switchMap", "throwError", "JwtRefreshInterceptor", "constructor", "authService", "intercept", "request", "next", "handle", "pipe", "error", "isLoginEndpoint", "url", "includes", "isRefreshEndpoint", "isLogoutEndpoint", "status", "refreshToken", "response", "updatedRequest", "clone", "setHeaders", "Authorization", "localStorage", "getItem", "<PERSON><PERSON>", "ACCESS_TOKEN", "refreshError", "logoutImmediate", "_", "i0", "ɵɵinject", "i1", "AuthService", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\jwt-refresh.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';\r\nimport { catchError, switchMap } from 'rxjs/operators';\r\nimport { throwError } from 'rxjs';\r\nimport { StorageUtil } from '../utils/storage.util';\r\nimport { AuthService } from '../services/auth.service';\r\n\r\n@Injectable()\r\nexport class JwtRefreshInterceptor implements HttpInterceptor {\r\n    constructor(private authService: AuthService) {}\r\n\r\n    intercept(request: HttpRequest<any>, next: HttpHandler) {\r\n        return next.handle(request).pipe(\r\n            catchError((error: HttpErrorResponse) => {\r\n                // Skip refresh logic for login, refresh, and logout endpoints\r\n                const isLoginEndpoint = request.url.includes('/api/auth/login');\r\n                const isRefreshEndpoint = request.url.includes('/api/auth/refresh');\r\n                const isLogoutEndpoint = request.url.includes('/api/auth/logout');\r\n\r\n                if (error.status === 401 && !isLoginEndpoint && !isRefreshEndpoint && !isLogoutEndpoint) {\r\n                    // Token expired or unauthorized; attempt to refresh it\r\n                    return this.authService.refreshToken().pipe(\r\n                        switchMap((response) => {\r\n                            // Retry the original request with the new token\r\n                            const updatedRequest = request.clone({\r\n                                setHeaders: {\r\n                                    Authorization: `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`,\r\n                                },\r\n                            });\r\n                            return next.handle(updatedRequest);\r\n                        }),\r\n                        catchError((refreshError) => {\r\n                            // Refresh token failed; log out the user immediately\r\n                            this.authService.logoutImmediate();\r\n                            return throwError(() => refreshError);\r\n                        }),\r\n                    );\r\n                }\r\n                return throwError(() => error);\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAASA,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AACtD,SAASC,UAAU,QAAQ,MAAM;;;AAKjC,OAAM,MAAOC,qBAAqB;EAC9BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC5BV,UAAU,CAAEW,KAAwB,IAAI;MACpC;MACA,MAAMC,eAAe,GAAGL,OAAO,CAACM,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC;MAC/D,MAAMC,iBAAiB,GAAGR,OAAO,CAACM,GAAG,CAACC,QAAQ,CAAC,mBAAmB,CAAC;MACnE,MAAME,gBAAgB,GAAGT,OAAO,CAACM,GAAG,CAACC,QAAQ,CAAC,kBAAkB,CAAC;MAEjE,IAAIH,KAAK,CAACM,MAAM,KAAK,GAAG,IAAI,CAACL,eAAe,IAAI,CAACG,iBAAiB,IAAI,CAACC,gBAAgB,EAAE;QACrF;QACA,OAAO,IAAI,CAACX,WAAW,CAACa,YAAY,EAAE,CAACR,IAAI,CACvCT,SAAS,CAAEkB,QAAQ,IAAI;UACnB;UACA,MAAMC,cAAc,GAAGb,OAAO,CAACc,KAAK,CAAC;YACjCC,UAAU,EAAE;cACRC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAACC,IAAI,CAACC,YAAY,CAAC;;WAEvE,CAAC;UACF,OAAOnB,IAAI,CAACC,MAAM,CAACW,cAAc,CAAC;QACtC,CAAC,CAAC,EACFpB,UAAU,CAAE4B,YAAY,IAAI;UACxB;UACA,IAAI,CAACvB,WAAW,CAACwB,eAAe,EAAE;UAClC,OAAO3B,UAAU,CAAC,MAAM0B,YAAY,CAAC;QACzC,CAAC,CAAC,CACL;;MAEL,OAAO1B,UAAU,CAAC,MAAMS,KAAK,CAAC;IAClC,CAAC,CAAC,CACL;EACL;EAAC,QAAAmB,CAAA,G;qBAjCQ3B,qBAAqB,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAArBhC,qBAAqB;IAAAiC,OAAA,EAArBjC,qBAAqB,CAAAkC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}