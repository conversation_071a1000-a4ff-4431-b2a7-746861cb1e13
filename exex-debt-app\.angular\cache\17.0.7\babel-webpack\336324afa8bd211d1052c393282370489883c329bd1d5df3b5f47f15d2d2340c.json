{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/app.layout.service\";\nimport * as i2 from \"@app/core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/button\";\nimport * as i5 from \"primeng/checkbox\";\nimport * as i6 from \"primeng/inputtext\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/password\";\nfunction LoginComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.errorMessage, \" \");\n  }\n}\nexport class LoginComponent {\n  constructor(layoutService, authService) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n    this.valCheck = ['remember'];\n    this.username = '';\n    this.password = '';\n    this.errorMessage = '';\n    this.isLoading = false;\n    this.authService.redirectToDashboard();\n  }\n  login() {\n    if (!this.username || !this.password) {\n      this.errorMessage = 'Vui lòng nhập đầy đủ thông tin đăng nhập';\n      return;\n    }\n    this.isLoading = true;\n    this.errorMessage = '';\n    const credentials = {\n      username: this.username,\n      password: this.password\n    };\n    this.authService.login(credentials).subscribe({\n      next: response => {\n        this.isLoading = false;\n        // Navigation is handled in the service\n      },\n\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = error.error?.message || 'Đăng nhập thất bại. Vui lòng thử lại.';\n        console.error('Login error:', error);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 23,\n    vars: 7,\n    consts: [[1, \"surface-ground\", \"flex\", \"align-items-center\", \"justify-content-center\", \"min-h-screen\", \"min-w-screen\", \"overflow-hidden\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [2, \"border-radius\", \"56px\", \"padding\", \"0.3rem\"], [1, \"w-full\", \"surface-card\", \"py-8\", \"px-5\", \"sm:px-8\", 2, \"border-radius\", \"53px\"], [1, \"text-center\", \"mb-5\"], [1, \"text-900\", \"text-3xl\", \"font-medium\", \"mb-3\"], [1, \"text-600\", \"font-medium\"], [\"class\", \"p-3 mb-3 text-red-500 bg-red-50 border border-red-200 rounded\", 4, \"ngIf\"], [\"for\", \"username\", 1, \"block\", \"text-900\", \"text-xl\", \"font-medium\", \"mb-2\"], [\"id\", \"username\", \"type\", \"text\", \"placeholder\", \"Nh\\u1EADp t\\u00EAn \\u0111\\u0103ng nh\\u1EADp\", \"pInputText\", \"\", 1, \"w-full\", \"md:w-30rem\", \"mb-5\", 2, \"padding\", \"1rem\", 3, \"ngModel\", \"disabled\", \"ngModelChange\"], [\"for\", \"password1\", 1, \"block\", \"text-900\", \"font-medium\", \"text-xl\", \"mb-2\"], [\"id\", \"password1\", \"placeholder\", \"Nh\\u1EADp m\\u1EADt kh\\u1EA9u\", \"styleClass\", \"mb-5\", \"inputStyleClass\", \"w-full p-3 md:w-30rem\", 3, \"ngModel\", \"toggleMask\", \"disabled\", \"ngModelChange\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"mb-5\", \"gap-5\"], [1, \"flex\", \"align-items-center\"], [\"id\", \"rememberme1\", \"styleClass\", \"mr-2\", 3, \"binary\"], [\"for\", \"rememberme1\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Sign In\", 1, \"w-full\", \"p-3\", \"text-xl\", 3, \"click\"], [1, \"p-3\", \"mb-3\", \"text-red-500\", \"bg-red-50\", \"border\", \"border-red-200\", \"rounded\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵtext(6, \"Welcome, exex-debt!\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"span\", 6);\n        i0.ɵɵtext(8, \"Sign in to continue\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\");\n        i0.ɵɵtemplate(10, LoginComponent_div_10_Template, 2, 1, \"div\", 7);\n        i0.ɵɵelementStart(11, \"label\", 8);\n        i0.ɵɵtext(12, \"T\\u00EAn \\u0111\\u0103ng nh\\u1EADp\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"input\", 9);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_13_listener($event) {\n          return ctx.username = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"label\", 10);\n        i0.ɵɵtext(15, \"M\\u1EADt kh\\u1EA9u\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"p-password\", 11);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_p_password_ngModelChange_16_listener($event) {\n          return ctx.password = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 12)(18, \"div\", 13);\n        i0.ɵɵelement(19, \"p-checkbox\", 14);\n        i0.ɵɵelementStart(20, \"label\", 15);\n        i0.ɵɵtext(21, \"Remember me\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(22, \"button\", 16);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_22_listener() {\n          return ctx.login();\n        });\n        i0.ɵɵelementEnd()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngModel\", ctx.username)(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngModel\", ctx.password)(\"toggleMask\", true)(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"binary\", true);\n      }\n    },\n    dependencies: [i3.NgIf, i4.ButtonDirective, i5.Checkbox, i6.InputText, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgModel, i8.Password],\n    styles: [\"[_nghost-%COMP%]     .pi-eye, [_nghost-%COMP%]     .pi-eye-slash {\\n  transform: scale(1.6);\\n  margin-right: 1rem;\\n  color: var(--primary-color) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29yZS9jb21wb25lbnRzL2F1dGgvbG9naW4vbG9naW4uY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNZOztFQUVJLHFCQUFBO0VBQ0Esa0JBQUE7RUFDQSxzQ0FBQTtBQUFoQiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgICAgICAgICAgOmhvc3QgOjpuZy1kZWVwIC5waS1leWUsXG4gICAgICAgICAgICA6aG9zdCA6Om5nLWRlZXAgLnBpLWV5ZS1zbGFzaCB7XG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjYpO1xuICAgICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMXJlbTtcbiAgICAgICAgICAgICAgICBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcikgIWltcG9ydGFudDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "errorMessage", "LoginComponent", "constructor", "layoutService", "authService", "val<PERSON><PERSON><PERSON>", "username", "password", "isLoading", "redirectToDashboard", "login", "credentials", "subscribe", "next", "response", "error", "message", "console", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "AuthService", "_2", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵtemplate", "LoginComponent_div_10_Template", "ɵɵlistener", "LoginComponent_Template_input_ngModelChange_13_listener", "$event", "LoginComponent_Template_p_password_ngModelChange_16_listener", "ɵɵelement", "LoginComponent_Template_button_click_22_listener", "ɵɵproperty"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.component.ts", "C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { AuthService } from '@app/core/services/auth.service';\r\nimport { LayoutService } from 'src/app/layout/app.layout.service';\r\nimport { LoginRequest } from '@app/core/models/auth.model';\r\n\r\n@Component({\r\n    selector: 'app-login',\r\n    templateUrl: './login.component.html',\r\n    styles: [\r\n        `\r\n            :host ::ng-deep .pi-eye,\r\n            :host ::ng-deep .pi-eye-slash {\r\n                transform: scale(1.6);\r\n                margin-right: 1rem;\r\n                color: var(--primary-color) !important;\r\n            }\r\n        `,\r\n    ],\r\n})\r\nexport class LoginComponent {\r\n    valCheck: string[] = ['remember'];\r\n    username: string = '';\r\n    password: string = '';\r\n    errorMessage: string = '';\r\n    isLoading: boolean = false;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private authService: AuthService,\r\n    ) {\r\n        this.authService.redirectToDashboard();\r\n    }\r\n\r\n    login() {\r\n        if (!this.username || !this.password) {\r\n            this.errorMessage = '<PERSON><PERSON> lòng nhập đ<PERSON>y đủ thông tin đăng nhập';\r\n            return;\r\n        }\r\n\r\n        this.isLoading = true;\r\n        this.errorMessage = '';\r\n\r\n        const credentials: LoginRequest = {\r\n            username: this.username,\r\n            password: this.password\r\n        };\r\n\r\n        this.authService.login(credentials).subscribe({\r\n            next: (response) => {\r\n                this.isLoading = false;\r\n                // Navigation is handled in the service\r\n            },\r\n            error: (error) => {\r\n                this.isLoading = false;\r\n                this.errorMessage = error.error?.message || 'Đăng nhập thất bại. Vui lòng thử lại.';\r\n                console.error('Login error:', error);\r\n            }\r\n        });\r\n    }\r\n}\r\n", "<div class=\"surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden\">\r\n    <div class=\"flex flex-column align-items-center justify-content-center\">\r\n        <div style=\"border-radius: 56px; padding: 0.3rem\">\r\n            <div class=\"w-full surface-card py-8 px-5 sm:px-8\" style=\"border-radius: 53px\">\r\n                <div class=\"text-center mb-5\">\r\n                    <div class=\"text-900 text-3xl font-medium mb-3\">Welcome, exex-debt!</div>\r\n                    <span class=\"text-600 font-medium\">Sign in to continue</span>\r\n                </div>\r\n\r\n                <div>\r\n                    <!-- Error message display -->\r\n                    <div *ngIf=\"errorMessage\" class=\"p-3 mb-3 text-red-500 bg-red-50 border border-red-200 rounded\">\r\n                        {{ errorMessage }}\r\n                    </div>\r\n\r\n                    <label for=\"username\" class=\"block text-900 text-xl font-medium mb-2\">Tên đăng nhập</label>\r\n                    <input\r\n                        id=\"username\"\r\n                        type=\"text\"\r\n                        placeholder=\"Nhập tên đăng nhập\"\r\n                        [(ngModel)]=\"username\"\r\n                        pInputText\r\n                        class=\"w-full md:w-30rem mb-5\"\r\n                        style=\"padding: 1rem\"\r\n                        [disabled]=\"isLoading\" />\r\n\r\n                    <label for=\"password1\" class=\"block text-900 font-medium text-xl mb-2\">Mật khẩu</label>\r\n                    <p-password\r\n                        id=\"password1\"\r\n                        [(ngModel)]=\"password\"\r\n                        placeholder=\"Nhập mật khẩu\"\r\n                        [toggleMask]=\"true\"\r\n                        styleClass=\"mb-5\"\r\n                        inputStyleClass=\"w-full p-3 md:w-30rem\"\r\n                        [disabled]=\"isLoading\"></p-password>\r\n\r\n                    <div class=\"flex align-items-center justify-content-between mb-5 gap-5\">\r\n                        <div class=\"flex align-items-center\">\r\n                            <p-checkbox id=\"rememberme1\" [binary]=\"true\" styleClass=\"mr-2\"></p-checkbox>\r\n                            <label for=\"rememberme1\">Remember me</label>\r\n                        </div>\r\n                        <!-- <a\r\n                            class=\"font-medium no-underline ml-2 text-right cursor-pointer\"\r\n                            style=\"color: var(--primary-color)\"\r\n                            >Forgot password?</a\r\n                        > -->\r\n                    </div>\r\n                    <button pButton pRipple label=\"Sign In\" class=\"w-full p-3 text-xl\" (click)=\"login()\"></button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": ";;;;;;;;;;;ICWoBA,EAAA,CAAAC,cAAA,cAAgG;IAC5FD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,YAAA,MACJ;;;ADMpB,OAAM,MAAOC,cAAc;EAOvBC,YACWC,aAA4B,EAC3BC,WAAwB;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,WAAW,GAAXA,WAAW;IARvB,KAAAC,QAAQ,GAAa,CAAC,UAAU,CAAC;IACjC,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAP,YAAY,GAAW,EAAE;IACzB,KAAAQ,SAAS,GAAY,KAAK;IAMtB,IAAI,CAACJ,WAAW,CAACK,mBAAmB,EAAE;EAC1C;EAEAC,KAAKA,CAAA;IACD,IAAI,CAAC,IAAI,CAACJ,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAClC,IAAI,CAACP,YAAY,GAAG,0CAA0C;MAC9D;;IAGJ,IAAI,CAACQ,SAAS,GAAG,IAAI;IACrB,IAAI,CAACR,YAAY,GAAG,EAAE;IAEtB,MAAMW,WAAW,GAAiB;MAC9BL,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA;KAClB;IAED,IAAI,CAACH,WAAW,CAACM,KAAK,CAACC,WAAW,CAAC,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGC,QAAQ,IAAI;QACf,IAAI,CAACN,SAAS,GAAG,KAAK;QACtB;MACJ,CAAC;;MACDO,KAAK,EAAGA,KAAK,IAAI;QACb,IAAI,CAACP,SAAS,GAAG,KAAK;QACtB,IAAI,CAACR,YAAY,GAAGe,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,uCAAuC;QACnFC,OAAO,CAACF,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACxC;KACH,CAAC;EACN;EAAC,QAAAG,CAAA,G;qBAvCQjB,cAAc,EAAAR,EAAA,CAAA0B,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA5B,EAAA,CAAA0B,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdvB,cAAc;IAAAwB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnB3BtC,EAAA,CAAAC,cAAA,aAAqH;QAKjDD,EAAA,CAAAE,MAAA,0BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACzEH,EAAA,CAAAC,cAAA,cAAmC;QAAAD,EAAA,CAAAE,MAAA,0BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGjEH,EAAA,CAAAC,cAAA,UAAK;QAEDD,EAAA,CAAAwC,UAAA,KAAAC,8BAAA,iBAEM;QAENzC,EAAA,CAAAC,cAAA,gBAAsE;QAAAD,EAAA,CAAAE,MAAA,yCAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC3FH,EAAA,CAAAC,cAAA,gBAQ6B;QAJzBD,EAAA,CAAA0C,UAAA,2BAAAC,wDAAAC,MAAA;UAAA,OAAAL,GAAA,CAAA1B,QAAA,GAAA+B,MAAA;QAAA,EAAsB;QAJ1B5C,EAAA,CAAAG,YAAA,EAQ6B;QAE7BH,EAAA,CAAAC,cAAA,iBAAuE;QAAAD,EAAA,CAAAE,MAAA,0BAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACvFH,EAAA,CAAAC,cAAA,sBAO2B;QALvBD,EAAA,CAAA0C,UAAA,2BAAAG,6DAAAD,MAAA;UAAA,OAAAL,GAAA,CAAAzB,QAAA,GAAA8B,MAAA;QAAA,EAAsB;QAKC5C,EAAA,CAAAG,YAAA,EAAa;QAExCH,EAAA,CAAAC,cAAA,eAAwE;QAEhED,EAAA,CAAA8C,SAAA,sBAA4E;QAC5E9C,EAAA,CAAAC,cAAA,iBAAyB;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAQpDH,EAAA,CAAAC,cAAA,kBAAqF;QAAlBD,EAAA,CAAA0C,UAAA,mBAAAK,iDAAA;UAAA,OAASR,GAAA,CAAAtB,KAAA,EAAO;QAAA,EAAC;QAACjB,EAAA,CAAAG,YAAA,EAAS;;;QApCxFH,EAAA,CAAAI,SAAA,IAAkB;QAAlBJ,EAAA,CAAAgD,UAAA,SAAAT,GAAA,CAAAhC,YAAA,CAAkB;QASpBP,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAgD,UAAA,YAAAT,GAAA,CAAA1B,QAAA,CAAsB,aAAA0B,GAAA,CAAAxB,SAAA;QAStBf,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAgD,UAAA,YAAAT,GAAA,CAAAzB,QAAA,CAAsB,iCAAAyB,GAAA,CAAAxB,SAAA;QASWf,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAgD,UAAA,gBAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}