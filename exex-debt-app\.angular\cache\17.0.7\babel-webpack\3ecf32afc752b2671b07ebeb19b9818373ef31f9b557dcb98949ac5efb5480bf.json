{"ast": null, "code": "import { AuthService } from '@app/core/services/auth.service';\nimport { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Path } from '../enums/path.enum';\nexport const authGuard = (_, state) => {\n  const authService = inject(AuthService);\n  const isAuth = authService.isAuthenticated();\n  const redirectToLogin = inject(Router).createUrlTree([Path.AUTH_LOGIN]);\n  console.log('AuthGuard check:', {\n    isAuthenticated: isAuth,\n    route: state.url,\n    token: localStorage.getItem('debt_access_token')\n  });\n  return isAuth ? true : redirectToLogin;\n};", "map": {"version": 3, "names": ["AuthService", "inject", "Router", "Path", "<PERSON>th<PERSON><PERSON>", "_", "state", "authService", "isAuth", "isAuthenticated", "redirectToLogin", "createUrlTree", "AUTH_LOGIN", "console", "log", "route", "url", "token", "localStorage", "getItem"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["import { AuthService } from '@app/core/services/auth.service';\r\nimport { inject } from '@angular/core';\r\nimport { CanActivateFn, Router } from '@angular/router';\r\nimport { Path } from '../enums/path.enum';\r\n\r\nexport const authGuard: CanActivateFn = (_, state) => {\r\n    const authService = inject(AuthService);\r\n    const isAuth = authService.isAuthenticated();\r\n    const redirectToLogin = inject(Router).createUrlTree([Path.AUTH_LOGIN]);\r\n\r\n    console.log('AuthGuard check:', {\r\n        isAuthenticated: isAuth,\r\n        route: state.url,\r\n        token: localStorage.getItem('debt_access_token')\r\n    });\r\n\r\n    return isAuth ? true : redirectToLogin;\r\n};\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,iCAAiC;AAC7D,SAASC,MAAM,QAAQ,eAAe;AACtC,SAAwBC,MAAM,QAAQ,iBAAiB;AACvD,SAASC,IAAI,QAAQ,oBAAoB;AAEzC,OAAO,MAAMC,SAAS,GAAkBA,CAACC,CAAC,EAAEC,KAAK,KAAI;EACjD,MAAMC,WAAW,GAAGN,MAAM,CAACD,WAAW,CAAC;EACvC,MAAMQ,MAAM,GAAGD,WAAW,CAACE,eAAe,EAAE;EAC5C,MAAMC,eAAe,GAAGT,MAAM,CAACC,MAAM,CAAC,CAACS,aAAa,CAAC,CAACR,IAAI,CAACS,UAAU,CAAC,CAAC;EAEvEC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;IAC5BL,eAAe,EAAED,MAAM;IACvBO,KAAK,EAAET,KAAK,CAACU,GAAG;IAChBC,KAAK,EAAEC,YAAY,CAACC,OAAO,CAAC,mBAAmB;GAClD,CAAC;EAEF,OAAOX,MAAM,GAAG,IAAI,GAAGE,eAAe;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}