{"ast": null, "code": "import { retry, catchError } from 'rxjs/operators';\nimport { throwError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nexport class RetryInterceptor {\n  constructor() {}\n  intercept(request, next) {\n    // Skip retry for auth endpoints and POST requests (to avoid duplicate submissions)\n    const isAuthEndpoint = request.url.includes('/api/auth/');\n    const isPostRequest = request.method === 'POST';\n    // Only retry GET requests and non-auth endpoints\n    if (isAuthEndpoint || isPostRequest) {\n      return next.handle(request);\n    }\n    // Define the maximum number of retries for safe operations only\n    const maxRetries = 2;\n    return next.handle(request).pipe(retry({\n      count: maxRetries,\n      delay: (error, retryCount) => {\n        // Only retry on network errors or 5xx server errors\n        if (error.status >= 500 || error.status === 0) {\n          console.log(`Retrying request (${retryCount}/${maxRetries}):`, request.url);\n          return throwError(() => error);\n        }\n        // Don't retry on 4xx client errors\n        throw error;\n      }\n    }), catchError(error => {\n      return throwError(() => error);\n    }));\n  }\n  static #_ = this.ɵfac = function RetryInterceptor_Factory(t) {\n    return new (t || RetryInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: RetryInterceptor,\n    factory: RetryInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["retry", "catchError", "throwError", "RetryInterceptor", "constructor", "intercept", "request", "next", "isAuthEndpoint", "url", "includes", "isPostRequest", "method", "handle", "maxRetries", "pipe", "count", "delay", "error", "retryCount", "status", "console", "log", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\retry.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';\r\nimport { retry, catchError } from 'rxjs/operators';\r\nimport { throwError } from 'rxjs';\r\n\r\n@Injectable()\r\nexport class RetryInterceptor implements HttpInterceptor {\r\n    constructor() {}\r\n\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler) {\r\n        // Skip retry for auth endpoints and POST requests (to avoid duplicate submissions)\r\n        const isAuthEndpoint = request.url.includes('/api/auth/');\r\n        const isPostRequest = request.method === 'POST';\r\n\r\n        // Only retry GET requests and non-auth endpoints\r\n        if (isAuthEndpoint || isPostRequest) {\r\n            return next.handle(request);\r\n        }\r\n\r\n        // Define the maximum number of retries for safe operations only\r\n        const maxRetries = 2;\r\n        return next.handle(request).pipe(\r\n            retry({\r\n                count: maxRetries,\r\n                delay: (error: HttpErrorResponse, retryCount: number) => {\r\n                    // Only retry on network errors or 5xx server errors\r\n                    if (error.status >= 500 || error.status === 0) {\r\n                        console.log(`Retrying request (${retryCount}/${maxRetries}):`, request.url);\r\n                        return throwError(() => error);\r\n                    }\r\n                    // Don't retry on 4xx client errors\r\n                    throw error;\r\n                }\r\n            }),\r\n            catchError((error: HttpErrorResponse) => {\r\n                return throwError(() => error);\r\n            })\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAASA,KAAK,EAAEC,UAAU,QAAQ,gBAAgB;AAClD,SAASC,UAAU,QAAQ,MAAM;;AAGjC,OAAM,MAAOC,gBAAgB;EACzBC,YAAA,GAAe;EAEfC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD;IACA,MAAMC,cAAc,GAAGF,OAAO,CAACG,GAAG,CAACC,QAAQ,CAAC,YAAY,CAAC;IACzD,MAAMC,aAAa,GAAGL,OAAO,CAACM,MAAM,KAAK,MAAM;IAE/C;IACA,IAAIJ,cAAc,IAAIG,aAAa,EAAE;MACjC,OAAOJ,IAAI,CAACM,MAAM,CAACP,OAAO,CAAC;;IAG/B;IACA,MAAMQ,UAAU,GAAG,CAAC;IACpB,OAAOP,IAAI,CAACM,MAAM,CAACP,OAAO,CAAC,CAACS,IAAI,CAC5Bf,KAAK,CAAC;MACFgB,KAAK,EAAEF,UAAU;MACjBG,KAAK,EAAEA,CAACC,KAAwB,EAAEC,UAAkB,KAAI;QACpD;QACA,IAAID,KAAK,CAACE,MAAM,IAAI,GAAG,IAAIF,KAAK,CAACE,MAAM,KAAK,CAAC,EAAE;UAC3CC,OAAO,CAACC,GAAG,CAAC,qBAAqBH,UAAU,IAAIL,UAAU,IAAI,EAAER,OAAO,CAACG,GAAG,CAAC;UAC3E,OAAOP,UAAU,CAAC,MAAMgB,KAAK,CAAC;;QAElC;QACA,MAAMA,KAAK;MACf;KACH,CAAC,EACFjB,UAAU,CAAEiB,KAAwB,IAAI;MACpC,OAAOhB,UAAU,CAAC,MAAMgB,KAAK,CAAC;IAClC,CAAC,CAAC,CACL;EACL;EAAC,QAAAK,CAAA,G;qBAhCQpB,gBAAgB;EAAA;EAAA,QAAAqB,EAAA,G;WAAhBrB,gBAAgB;IAAAsB,OAAA,EAAhBtB,gBAAgB,CAAAuB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}