{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Observable, tap, catchError, throwError } from 'rxjs';\nimport { Auth } from '../enums/auth.enum';\nimport { Path } from '../enums/path.enum';\nimport { StorageUtil } from '../utils/storage.util';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"ngx-spinner\";\nexport class AuthService {\n  constructor(http, spinner) {\n    this.http = http;\n    this.spinner = spinner;\n    this.redirectTo = inject(Router);\n    const token = StorageUtil.getAccessToken();\n    if (token) {\n      this.jwtToken = token;\n    }\n  }\n  refreshToken() {\n    const refreshToken = StorageUtil.getRefreshToken();\n    if (!refreshToken) {\n      return throwError(() => new Error('No refresh token available'));\n    }\n    return this.http.post('api/auth/refresh', {\n      refreshToken\n    }).pipe(tap(response => {\n      if (response.success && response.data) {\n        StorageUtil.setTokens(response.data.accessToken, response.data.refreshToken);\n        this.jwtToken = response.data.accessToken;\n      }\n    }), catchError(error => {\n      this.logoutImmediate();\n      return throwError(() => error);\n    }));\n  }\n  isAuthenticated() {\n    // Check both in-memory token and localStorage\n    const token = this.jwtToken || StorageUtil.getAccessToken();\n    if (!token) {\n      return false;\n    }\n    // Update in-memory token if it's missing but exists in localStorage\n    if (!this.jwtToken && token) {\n      this.jwtToken = token;\n    }\n    return true;\n  }\n  redirectToDashboard() {\n    this.jwtToken = StorageUtil.getAccessToken();\n    this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\n  }\n  login(credentials) {\n    this.spinner.show();\n    return this.http.post('api/auth/login', credentials).pipe(tap(response => {\n      if (response.success && response.data) {\n        this.jwtToken = response.data.accessToken;\n        StorageUtil.setTokens(response.data.accessToken, response.data.refreshToken);\n        this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\n      }\n      this.spinner.hide();\n    }), catchError(error => {\n      this.spinner.hide();\n      return throwError(() => error);\n    }));\n  }\n  logout() {\n    console.log('Logout initiated');\n    return this.http.post('api/auth/logout', {}).pipe(tap(response => {\n      console.log('Logout API success:', response);\n      // Clear tokens regardless of API response\n      this.clearTokensAndRedirect();\n    }), catchError(error => {\n      console.log('Logout API failed:', error);\n      // Clear tokens even if logout API fails\n      this.clearTokensAndRedirect();\n      // Don't throw error, just complete successfully\n      return new Observable(subscriber => {\n        subscriber.next({\n          success: true,\n          message: 'Đăng xuất thành công (local)',\n          data: 'Logged out locally'\n        });\n        subscriber.complete();\n      });\n    }));\n  }\n  clearTokensAndRedirect() {\n    console.log('Clearing tokens and redirecting');\n    this.jwtToken = null;\n    localStorage.removeItem(Auth.ACCESS_TOKEN);\n    localStorage.removeItem(Auth.REFRESH_TOKEN);\n    this.redirectTo.navigate([Path.AUTH_LOGIN]);\n  }\n  // Method for immediate logout without API call (for error scenarios)\n  logoutImmediate() {\n    console.log('Immediate logout');\n    this.clearTokensAndRedirect();\n  }\n  mockUser() {\n    return this.http.get('https://jsonplaceholder.typicode.com/users');\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.NgxSpinnerService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "Router", "Observable", "tap", "catchError", "throwError", "<PERSON><PERSON>", "Path", "StorageUtil", "AuthService", "constructor", "http", "spinner", "redirectTo", "token", "getAccessToken", "jwtToken", "refreshToken", "getRefreshToken", "Error", "post", "pipe", "response", "success", "data", "setTokens", "accessToken", "error", "logoutImmediate", "isAuthenticated", "redirectToDashboard", "navigate", "DASHBOARD_CUSTOMER", "login", "credentials", "show", "hide", "logout", "console", "log", "clearTokensAndRedirect", "subscriber", "next", "message", "complete", "localStorage", "removeItem", "ACCESS_TOKEN", "REFRESH_TOKEN", "AUTH_LOGIN", "mockUser", "get", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "NgxSpinnerService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NgxSpinnerService } from 'ngx-spinner';\r\nimport { Observable, tap, catchError, throwError } from 'rxjs';\r\nimport { Auth } from '../enums/auth.enum';\r\nimport { Path } from '../enums/path.enum';\r\nimport { LoginRequest, LoginResponse, LogoutResponse, RefreshTokenResponse } from '../models/auth.model';\r\nimport { StorageUtil } from '../utils/storage.util';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n    private jwtToken;\r\n    redirectTo = inject(Router);\r\n\r\n    constructor(\r\n        private http: HttpClient,\r\n        private spinner: NgxSpinnerService,\r\n    ) {\r\n        const token = StorageUtil.getAccessToken();\r\n        if (token) {\r\n            this.jwtToken = token;\r\n        }\r\n    }\r\n\r\n    refreshToken(): Observable<RefreshTokenResponse> {\r\n        const refreshToken = StorageUtil.getRefreshToken();\r\n        if (!refreshToken) {\r\n            return throwError(() => new Error('No refresh token available'));\r\n        }\r\n\r\n        return this.http.post<RefreshTokenResponse>('api/auth/refresh', { refreshToken }).pipe(\r\n            tap((response) => {\r\n                if (response.success && response.data) {\r\n                    StorageUtil.setTokens(response.data.accessToken, response.data.refreshToken);\r\n                    this.jwtToken = response.data.accessToken;\r\n                }\r\n            }),\r\n            catchError((error) => {\r\n                this.logoutImmediate();\r\n                return throwError(() => error);\r\n            })\r\n        );\r\n    }\r\n\r\n    public isAuthenticated(): boolean {\r\n        // Check both in-memory token and localStorage\r\n        const token = this.jwtToken || StorageUtil.getAccessToken();\r\n        if (!token) {\r\n            return false;\r\n        }\r\n\r\n        // Update in-memory token if it's missing but exists in localStorage\r\n        if (!this.jwtToken && token) {\r\n            this.jwtToken = token;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    redirectToDashboard() {\r\n        this.jwtToken = StorageUtil.getAccessToken();\r\n        this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\r\n    }\r\n\r\n    login(credentials: LoginRequest): Observable<LoginResponse> {\r\n        this.spinner.show();\r\n\r\n        return this.http.post<LoginResponse>('api/auth/login', credentials).pipe(\r\n            tap((response) => {\r\n                if (response.success && response.data) {\r\n                    this.jwtToken = response.data.accessToken;\r\n                    StorageUtil.setTokens(response.data.accessToken, response.data.refreshToken);\r\n                    this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\r\n                }\r\n                this.spinner.hide();\r\n            }),\r\n            catchError((error) => {\r\n                this.spinner.hide();\r\n                return throwError(() => error);\r\n            })\r\n        );\r\n    }\r\n\r\n    logout(): Observable<LogoutResponse> {\r\n        console.log('Logout initiated');\r\n\r\n        return this.http.post<LogoutResponse>('api/auth/logout', {}).pipe(\r\n            tap((response) => {\r\n                console.log('Logout API success:', response);\r\n                // Clear tokens regardless of API response\r\n                this.clearTokensAndRedirect();\r\n            }),\r\n            catchError((error) => {\r\n                console.log('Logout API failed:', error);\r\n                // Clear tokens even if logout API fails\r\n                this.clearTokensAndRedirect();\r\n                // Don't throw error, just complete successfully\r\n                return new Observable<LogoutResponse>(subscriber => {\r\n                    subscriber.next({\r\n                        success: true,\r\n                        message: 'Đăng xuất thành công (local)',\r\n                        data: 'Logged out locally'\r\n                    });\r\n                    subscriber.complete();\r\n                });\r\n            })\r\n        );\r\n    }\r\n\r\n    private clearTokensAndRedirect(): void {\r\n        console.log('Clearing tokens and redirecting');\r\n        this.jwtToken = null;\r\n        localStorage.removeItem(Auth.ACCESS_TOKEN);\r\n        localStorage.removeItem(Auth.REFRESH_TOKEN);\r\n        this.redirectTo.navigate([Path.AUTH_LOGIN]);\r\n    }\r\n\r\n    // Method for immediate logout without API call (for error scenarios)\r\n    logoutImmediate(): void {\r\n        console.log('Immediate logout');\r\n        this.clearTokensAndRedirect();\r\n    }\r\n\r\n    mockUser(): Observable<any> {\r\n        return this.http.get('https://jsonplaceholder.typicode.com/users');\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,MAAM,QAAoB,eAAe;AAClD,SAASC,MAAM,QAAQ,iBAAiB;AAExC,SAASC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,UAAU,QAAQ,MAAM;AAC9D,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,IAAI,QAAQ,oBAAoB;AAEzC,SAASC,WAAW,QAAQ,uBAAuB;;;;AAKnD,OAAM,MAAOC,WAAW;EAIpBC,YACYC,IAAgB,EAChBC,OAA0B;IAD1B,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,OAAO,GAAPA,OAAO;IAJnB,KAAAC,UAAU,GAAGb,MAAM,CAACC,MAAM,CAAC;IAMvB,MAAMa,KAAK,GAAGN,WAAW,CAACO,cAAc,EAAE;IAC1C,IAAID,KAAK,EAAE;MACP,IAAI,CAACE,QAAQ,GAAGF,KAAK;;EAE7B;EAEAG,YAAYA,CAAA;IACR,MAAMA,YAAY,GAAGT,WAAW,CAACU,eAAe,EAAE;IAClD,IAAI,CAACD,YAAY,EAAE;MACf,OAAOZ,UAAU,CAAC,MAAM,IAAIc,KAAK,CAAC,4BAA4B,CAAC,CAAC;;IAGpE,OAAO,IAAI,CAACR,IAAI,CAACS,IAAI,CAAuB,kBAAkB,EAAE;MAAEH;IAAY,CAAE,CAAC,CAACI,IAAI,CAClFlB,GAAG,CAAEmB,QAAQ,IAAI;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACnChB,WAAW,CAACiB,SAAS,CAACH,QAAQ,CAACE,IAAI,CAACE,WAAW,EAAEJ,QAAQ,CAACE,IAAI,CAACP,YAAY,CAAC;QAC5E,IAAI,CAACD,QAAQ,GAAGM,QAAQ,CAACE,IAAI,CAACE,WAAW;;IAEjD,CAAC,CAAC,EACFtB,UAAU,CAAEuB,KAAK,IAAI;MACjB,IAAI,CAACC,eAAe,EAAE;MACtB,OAAOvB,UAAU,CAAC,MAAMsB,KAAK,CAAC;IAClC,CAAC,CAAC,CACL;EACL;EAEOE,eAAeA,CAAA;IAClB;IACA,MAAMf,KAAK,GAAG,IAAI,CAACE,QAAQ,IAAIR,WAAW,CAACO,cAAc,EAAE;IAC3D,IAAI,CAACD,KAAK,EAAE;MACR,OAAO,KAAK;;IAGhB;IACA,IAAI,CAAC,IAAI,CAACE,QAAQ,IAAIF,KAAK,EAAE;MACzB,IAAI,CAACE,QAAQ,GAAGF,KAAK;;IAGzB,OAAO,IAAI;EACf;EAEAgB,mBAAmBA,CAAA;IACf,IAAI,CAACd,QAAQ,GAAGR,WAAW,CAACO,cAAc,EAAE;IAC5C,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACH,UAAU,CAACkB,QAAQ,CAAC,CAACxB,IAAI,CAACyB,kBAAkB,CAAC,CAAC;EACxE;EAEAC,KAAKA,CAACC,WAAyB;IAC3B,IAAI,CAACtB,OAAO,CAACuB,IAAI,EAAE;IAEnB,OAAO,IAAI,CAACxB,IAAI,CAACS,IAAI,CAAgB,gBAAgB,EAAEc,WAAW,CAAC,CAACb,IAAI,CACpElB,GAAG,CAAEmB,QAAQ,IAAI;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACnC,IAAI,CAACR,QAAQ,GAAGM,QAAQ,CAACE,IAAI,CAACE,WAAW;QACzClB,WAAW,CAACiB,SAAS,CAACH,QAAQ,CAACE,IAAI,CAACE,WAAW,EAAEJ,QAAQ,CAACE,IAAI,CAACP,YAAY,CAAC;QAC5E,IAAI,CAACJ,UAAU,CAACkB,QAAQ,CAAC,CAACxB,IAAI,CAACyB,kBAAkB,CAAC,CAAC;;MAEvD,IAAI,CAACpB,OAAO,CAACwB,IAAI,EAAE;IACvB,CAAC,CAAC,EACFhC,UAAU,CAAEuB,KAAK,IAAI;MACjB,IAAI,CAACf,OAAO,CAACwB,IAAI,EAAE;MACnB,OAAO/B,UAAU,CAAC,MAAMsB,KAAK,CAAC;IAClC,CAAC,CAAC,CACL;EACL;EAEAU,MAAMA,CAAA;IACFC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAE/B,OAAO,IAAI,CAAC5B,IAAI,CAACS,IAAI,CAAiB,iBAAiB,EAAE,EAAE,CAAC,CAACC,IAAI,CAC7DlB,GAAG,CAAEmB,QAAQ,IAAI;MACbgB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEjB,QAAQ,CAAC;MAC5C;MACA,IAAI,CAACkB,sBAAsB,EAAE;IACjC,CAAC,CAAC,EACFpC,UAAU,CAAEuB,KAAK,IAAI;MACjBW,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEZ,KAAK,CAAC;MACxC;MACA,IAAI,CAACa,sBAAsB,EAAE;MAC7B;MACA,OAAO,IAAItC,UAAU,CAAiBuC,UAAU,IAAG;QAC/CA,UAAU,CAACC,IAAI,CAAC;UACZnB,OAAO,EAAE,IAAI;UACboB,OAAO,EAAE,8BAA8B;UACvCnB,IAAI,EAAE;SACT,CAAC;QACFiB,UAAU,CAACG,QAAQ,EAAE;MACzB,CAAC,CAAC;IACN,CAAC,CAAC,CACL;EACL;EAEQJ,sBAAsBA,CAAA;IAC1BF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACvB,QAAQ,GAAG,IAAI;IACpB6B,YAAY,CAACC,UAAU,CAACxC,IAAI,CAACyC,YAAY,CAAC;IAC1CF,YAAY,CAACC,UAAU,CAACxC,IAAI,CAAC0C,aAAa,CAAC;IAC3C,IAAI,CAACnC,UAAU,CAACkB,QAAQ,CAAC,CAACxB,IAAI,CAAC0C,UAAU,CAAC,CAAC;EAC/C;EAEA;EACArB,eAAeA,CAAA;IACXU,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B,IAAI,CAACC,sBAAsB,EAAE;EACjC;EAEAU,QAAQA,CAAA;IACJ,OAAO,IAAI,CAACvC,IAAI,CAACwC,GAAG,CAAC,4CAA4C,CAAC;EACtE;EAAC,QAAAC,CAAA,G;qBAnHQ3C,WAAW,EAAA4C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXlD,WAAW;IAAAmD,OAAA,EAAXnD,WAAW,CAAAoD,IAAA;IAAAC,UAAA,EAFR;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}