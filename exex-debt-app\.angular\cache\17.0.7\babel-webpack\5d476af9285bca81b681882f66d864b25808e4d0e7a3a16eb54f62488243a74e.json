{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class HeadersInterceptor {\n  intercept(request, next) {\n    // Only add Content-Type if not already set, and preserve existing headers\n    let headers = request.headers;\n    if (!headers.has('Content-Type')) {\n      headers = headers.set('Content-Type', 'application/json');\n    }\n    const headersRequest = request.clone({\n      headers\n    });\n    return next.handle(headersRequest);\n  }\n  static #_ = this.ɵfac = function HeadersInterceptor_Factory(t) {\n    return new (t || HeadersInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: HeadersInterceptor,\n    factory: HeadersInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["HeadersInterceptor", "intercept", "request", "next", "headers", "has", "set", "headersRequest", "clone", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\headers.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';\r\n\r\n@Injectable()\r\nexport class HeadersInterceptor implements HttpInterceptor {\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>) {\r\n        // Only add Content-Type if not already set, and preserve existing headers\r\n        let headers = request.headers;\r\n\r\n        if (!headers.has('Content-Type')) {\r\n            headers = headers.set('Content-Type', 'application/json');\r\n        }\r\n\r\n        const headersRequest = request.clone({ headers });\r\n        return next.handle(headersRequest);\r\n    }\r\n}\r\n"], "mappings": ";AAIA,OAAM,MAAOA,kBAAkB;EAC3BC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD;IACA,IAAIC,OAAO,GAAGF,OAAO,CAACE,OAAO;IAE7B,IAAI,CAACA,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC,EAAE;MAC9BD,OAAO,GAAGA,OAAO,CAACE,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC;;IAG7D,MAAMC,cAAc,GAAGL,OAAO,CAACM,KAAK,CAAC;MAAEJ;IAAO,CAAE,CAAC;IACjD,OAAOD,IAAI,CAACM,MAAM,CAACF,cAAc,CAAC;EACtC;EAAC,QAAAG,CAAA,G;qBAXQV,kBAAkB;EAAA;EAAA,QAAAW,EAAA,G;WAAlBX,kBAAkB;IAAAY,OAAA,EAAlBZ,kBAAkB,CAAAa;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}