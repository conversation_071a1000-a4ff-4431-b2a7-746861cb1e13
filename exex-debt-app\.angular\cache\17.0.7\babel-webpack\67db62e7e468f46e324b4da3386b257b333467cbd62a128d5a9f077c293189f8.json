{"ast": null, "code": "import { LocationStrategy, PathLocationStrategy } from '@angular/common';\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { NgxSpinnerModule } from 'ngx-spinner';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { NotfoundComponent } from './core/components/notfound/notfound.component';\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\nimport { BaseUrlInterceptor } from './core/interceptors/base-url.interceptor';\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\nimport { HeadersInterceptor } from './core/interceptors/headers.interceptor';\nimport { JwtRefreshInterceptor } from './core/interceptors/jwt-refresh.interceptor';\nimport { LoadingInterceptor } from './core/interceptors/loading.interceptor';\nimport { LoggingInterceptor } from './core/interceptors/logging.interceptor';\nimport { OfflineModeInterceptor } from './core/interceptors/offline-mode.interceptor';\nimport { RequestTimingInterceptor } from './core/interceptors/request-timing.interceptor';\nimport { RetryInterceptor } from './core/interceptors/retry.interceptor';\nimport { TimeoutInterceptor } from './core/interceptors/timeout.interceptor';\nimport { AppLayoutModule } from './layout/app.layout.module';\nimport { TranslocoRootModule } from './transloco-root.module';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmationService, MessageService } from 'primeng/api';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static #_ = this.ɵfac = function AppModule_Factory(t) {\n    return new (t || AppModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AppModule,\n    bootstrap: [AppComponent]\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [MessageService, ConfirmationService, {\n      provide: LocationStrategy,\n      useClass: PathLocationStrategy\n    },\n    // Base URL should be first to set correct URLs\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: BaseUrlInterceptor,\n      multi: true\n    },\n    // Headers should be early to set content-type\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: HeadersInterceptor,\n      multi: true\n    },\n    // Auth interceptor to add Authorization header\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: AuthInterceptor,\n      multi: true\n    },\n    // JWT refresh should come after auth interceptor\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: JwtRefreshInterceptor,\n      multi: true\n    },\n    // Error handling\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: ErrorInterceptor,\n      multi: true\n    },\n    // Timeout\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: TimeoutInterceptor,\n      multi: true\n    },\n    // Retry\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: RetryInterceptor,\n      multi: true\n    },\n    // Loading indicator\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: LoadingInterceptor,\n      multi: true\n    },\n    // Offline mode\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: OfflineModeInterceptor,\n      multi: true\n    },\n    // Logging should be last to see final requests\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: LoggingInterceptor,\n      multi: true\n    },\n    // Request timing\n    {\n      provide: HTTP_INTERCEPTORS,\n      useClass: RequestTimingInterceptor,\n      multi: true\n    }],\n    imports: [AppRoutingModule, AppLayoutModule, NgxSpinnerModule, BrowserAnimationsModule, BrowserModule, HttpClientModule, TranslocoRootModule, ToastModule, ConfirmDialogModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, NotfoundComponent],\n    imports: [AppRoutingModule, AppLayoutModule, NgxSpinnerModule, BrowserAnimationsModule, BrowserModule, HttpClientModule, TranslocoRootModule, ToastModule, ConfirmDialogModule]\n  });\n})();", "map": {"version": 3, "names": ["LocationStrategy", "PathLocationStrategy", "HTTP_INTERCEPTORS", "HttpClientModule", "BrowserModule", "BrowserAnimationsModule", "NgxSpinnerModule", "AppRoutingModule", "AppComponent", "NotfoundComponent", "AuthInterceptor", "BaseUrlInterceptor", "ErrorInterceptor", "HeadersInterceptor", "JwtRefreshInterceptor", "LoadingInterceptor", "LoggingInterceptor", "OfflineModeInterceptor", "RequestTimingInterceptor", "RetryInterceptor", "TimeoutInterceptor", "AppLayoutModule", "TranslocoRootModule", "ToastModule", "ConfirmationService", "MessageService", "ConfirmDialogModule", "AppModule", "_", "_2", "bootstrap", "_3", "provide", "useClass", "multi", "imports", "declarations"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\app.module.ts"], "sourcesContent": ["import { LocationStrategy, PathLocationStrategy } from '@angular/common';\r\nimport { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';\r\nimport { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { NgxSpinnerModule } from 'ngx-spinner';\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { NotfoundComponent } from './core/components/notfound/notfound.component';\r\nimport { AuthInterceptor } from './core/interceptors/auth.interceptor';\r\nimport { BaseUrlInterceptor } from './core/interceptors/base-url.interceptor';\r\nimport { ErrorInterceptor } from './core/interceptors/error.interceptor';\r\nimport { HeadersInterceptor } from './core/interceptors/headers.interceptor';\r\nimport { JwtRefreshInterceptor } from './core/interceptors/jwt-refresh.interceptor';\r\nimport { LoadingInterceptor } from './core/interceptors/loading.interceptor';\r\nimport { LoggingInterceptor } from './core/interceptors/logging.interceptor';\r\nimport { OfflineModeInterceptor } from './core/interceptors/offline-mode.interceptor';\r\nimport { RequestTimingInterceptor } from './core/interceptors/request-timing.interceptor';\r\nimport { RetryInterceptor } from './core/interceptors/retry.interceptor';\r\nimport { TimeoutInterceptor } from './core/interceptors/timeout.interceptor';\r\nimport { AppLayoutModule } from './layout/app.layout.module';\r\nimport { TranslocoRootModule } from './transloco-root.module';\r\nimport { ToastModule } from 'primeng/toast';\r\nimport { ConfirmationService, MessageService } from 'primeng/api';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\n\r\n@NgModule({\r\n    declarations: [AppComponent, NotfoundComponent],\r\n    imports: [\r\n        AppRoutingModule,\r\n        AppLayoutModule,\r\n        NgxSpinnerModule,\r\n        BrowserAnimationsModule,\r\n        BrowserModule,\r\n        HttpClientModule,\r\n        TranslocoRootModule,\r\n        ToastModule,\r\n        ConfirmDialogModule,\r\n    ],\r\n    providers: [\r\n        MessageService,\r\n        ConfirmationService,\r\n        { provide: LocationStrategy, useClass: PathLocationStrategy },\r\n        // Base URL should be first to set correct URLs\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: BaseUrlInterceptor,\r\n            multi: true,\r\n        },\r\n        // Headers should be early to set content-type\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: HeadersInterceptor,\r\n            multi: true,\r\n        },\r\n        // Auth interceptor to add Authorization header\r\n        { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },\r\n        // JWT refresh should come after auth interceptor\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: JwtRefreshInterceptor,\r\n            multi: true,\r\n        },\r\n        // Error handling\r\n        { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },\r\n        // Timeout\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: TimeoutInterceptor,\r\n            multi: true,\r\n        },\r\n        // Retry\r\n        { provide: HTTP_INTERCEPTORS, useClass: RetryInterceptor, multi: true },\r\n        // Loading indicator\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: LoadingInterceptor,\r\n            multi: true,\r\n        },\r\n        // Offline mode\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: OfflineModeInterceptor,\r\n            multi: true,\r\n        },\r\n        // Logging should be last to see final requests\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: LoggingInterceptor,\r\n            multi: true,\r\n        },\r\n        // Request timing\r\n        {\r\n            provide: HTTP_INTERCEPTORS,\r\n            useClass: RequestTimingInterceptor,\r\n            multi: true,\r\n        },\r\n    ],\r\n    bootstrap: [AppComponent],\r\n    schemas: [CUSTOM_ELEMENTS_SCHEMA],\r\n})\r\nexport class AppModule {}\r\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,oBAAoB,QAAQ,iBAAiB;AACxE,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,sBAAsB;AAE1E,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,+CAA+C;AACjF,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,0CAA0C;AAC7E,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,qBAAqB,QAAQ,6CAA6C;AACnF,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,sBAAsB,QAAQ,8CAA8C;AACrF,SAASC,wBAAwB,QAAQ,gDAAgD;AACzF,SAASC,gBAAgB,QAAQ,uCAAuC;AACxE,SAASC,kBAAkB,QAAQ,yCAAyC;AAC5E,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,aAAa;AACjE,SAASC,mBAAmB,QAAQ,uBAAuB;;AA6E3D,OAAM,MAAOC,SAAS;EAAA,QAAAC,CAAA,G;qBAATD,SAAS;EAAA;EAAA,QAAAE,EAAA,G;UAATF,SAAS;IAAAG,SAAA,GAHNtB,YAAY;EAAA;EAAA,QAAAuB,EAAA,G;eA3Db,CACPN,cAAc,EACdD,mBAAmB,EACnB;MAAEQ,OAAO,EAAEhC,gBAAgB;MAAEiC,QAAQ,EAAEhC;IAAoB,CAAE;IAC7D;IACA;MACI+B,OAAO,EAAE9B,iBAAiB;MAC1B+B,QAAQ,EAAEtB,kBAAkB;MAC5BuB,KAAK,EAAE;KACV;IACD;IACA;MACIF,OAAO,EAAE9B,iBAAiB;MAC1B+B,QAAQ,EAAEpB,kBAAkB;MAC5BqB,KAAK,EAAE;KACV;IACD;IACA;MAAEF,OAAO,EAAE9B,iBAAiB;MAAE+B,QAAQ,EAAEvB,eAAe;MAAEwB,KAAK,EAAE;IAAI,CAAE;IACtE;IACA;MACIF,OAAO,EAAE9B,iBAAiB;MAC1B+B,QAAQ,EAAEnB,qBAAqB;MAC/BoB,KAAK,EAAE;KACV;IACD;IACA;MAAEF,OAAO,EAAE9B,iBAAiB;MAAE+B,QAAQ,EAAErB,gBAAgB;MAAEsB,KAAK,EAAE;IAAI,CAAE;IACvE;IACA;MACIF,OAAO,EAAE9B,iBAAiB;MAC1B+B,QAAQ,EAAEb,kBAAkB;MAC5Bc,KAAK,EAAE;KACV;IACD;IACA;MAAEF,OAAO,EAAE9B,iBAAiB;MAAE+B,QAAQ,EAAEd,gBAAgB;MAAEe,KAAK,EAAE;IAAI,CAAE;IACvE;IACA;MACIF,OAAO,EAAE9B,iBAAiB;MAC1B+B,QAAQ,EAAElB,kBAAkB;MAC5BmB,KAAK,EAAE;KACV;IACD;IACA;MACIF,OAAO,EAAE9B,iBAAiB;MAC1B+B,QAAQ,EAAEhB,sBAAsB;MAChCiB,KAAK,EAAE;KACV;IACD;IACA;MACIF,OAAO,EAAE9B,iBAAiB;MAC1B+B,QAAQ,EAAEjB,kBAAkB;MAC5BkB,KAAK,EAAE;KACV;IACD;IACA;MACIF,OAAO,EAAE9B,iBAAiB;MAC1B+B,QAAQ,EAAEf,wBAAwB;MAClCgB,KAAK,EAAE;KACV,CACJ;IAAAC,OAAA,GApEG5B,gBAAgB,EAChBc,eAAe,EACff,gBAAgB,EAChBD,uBAAuB,EACvBD,aAAa,EACbD,gBAAgB,EAChBmB,mBAAmB,EACnBC,WAAW,EACXG,mBAAmB;EAAA;;;2EAgEdC,SAAS;IAAAS,YAAA,GA1EH5B,YAAY,EAAEC,iBAAiB;IAAA0B,OAAA,GAE1C5B,gBAAgB,EAChBc,eAAe,EACff,gBAAgB,EAChBD,uBAAuB,EACvBD,aAAa,EACbD,gBAAgB,EAChBmB,mBAAmB,EACnBC,WAAW,EACXG,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}