{"ast": null, "code": "import { HttpResponse } from '@angular/common/http';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nexport class LoggingInterceptor {\n  intercept(request, next) {\n    // Log outgoing requests\n    console.log(`🚀 HTTP ${request.method} Request:`, request.url);\n    return next.handle(request).pipe(tap(event => {\n      if (event instanceof HttpResponse) {\n        console.log(`✅ HTTP ${request.method} Response (${event.status}):`, request.url);\n      }\n    }));\n  }\n  static #_ = this.ɵfac = function LoggingInterceptor_Factory(t) {\n    return new (t || LoggingInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: LoggingInterceptor,\n    factory: LoggingInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["HttpResponse", "tap", "LoggingInterceptor", "intercept", "request", "next", "console", "log", "method", "url", "handle", "pipe", "event", "status", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\logging.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpResponse } from '@angular/common/http';\r\nimport { tap } from 'rxjs/operators';\r\n\r\n@Injectable()\r\nexport class LoggingInterceptor implements HttpInterceptor {\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>) {\r\n        // Log outgoing requests\r\n        console.log(`🚀 HTTP ${request.method} Request:`, request.url);\r\n\r\n        return next.handle(request).pipe(\r\n            tap((event) => {\r\n                if (event instanceof HttpResponse) {\r\n                    console.log(`✅ HTTP ${request.method} Response (${event.status}):`, request.url);\r\n                }\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AACA,SAAoDA,YAAY,QAAQ,sBAAsB;AAC9F,SAASC,GAAG,QAAQ,gBAAgB;;AAGpC,OAAM,MAAOC,kBAAkB;EAC3BC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD;IACAC,OAAO,CAACC,GAAG,CAAC,WAAWH,OAAO,CAACI,MAAM,WAAW,EAAEJ,OAAO,CAACK,GAAG,CAAC;IAE9D,OAAOJ,IAAI,CAACK,MAAM,CAACN,OAAO,CAAC,CAACO,IAAI,CAC5BV,GAAG,CAAEW,KAAK,IAAI;MACV,IAAIA,KAAK,YAAYZ,YAAY,EAAE;QAC/BM,OAAO,CAACC,GAAG,CAAC,UAAUH,OAAO,CAACI,MAAM,cAAcI,KAAK,CAACC,MAAM,IAAI,EAAET,OAAO,CAACK,GAAG,CAAC;;IAExF,CAAC,CAAC,CACL;EACL;EAAC,QAAAK,CAAA,G;qBAZQZ,kBAAkB;EAAA;EAAA,QAAAa,EAAA,G;WAAlBb,kBAAkB;IAAAc,OAAA,EAAlBd,kBAAkB,CAAAe;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}