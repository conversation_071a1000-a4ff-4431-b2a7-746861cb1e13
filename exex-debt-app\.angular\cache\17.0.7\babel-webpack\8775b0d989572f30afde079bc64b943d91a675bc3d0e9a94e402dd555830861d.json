{"ast": null, "code": "import { AuthService } from '@app/core/services/auth.service';\n//ng generate interceptor my-interceptor --skip-tests\nimport { inject } from '@angular/core';\nimport { Auth } from '../enums/auth.enum';\nimport * as i0 from \"@angular/core\";\nexport class AuthInterceptor {\n  constructor() {\n    this.authService = inject(AuthService);\n  }\n  intercept(request, next) {\n    // Skip adding Authorization header for auth endpoints\n    const isAuthEndpoint = request.url.includes('/api/auth/');\n    const token = localStorage.getItem(Auth.ACCESS_TOKEN);\n    if (!isAuthEndpoint && token) {\n      const authRequest = request.clone({\n        headers: request.headers.set('Authorization', `Bearer ${token}`)\n      });\n      return next.handle(authRequest);\n    }\n    return next.handle(request);\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(t) {\n    return new (t || AuthInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["AuthService", "inject", "<PERSON><PERSON>", "AuthInterceptor", "constructor", "authService", "intercept", "request", "next", "isAuthEndpoint", "url", "includes", "token", "localStorage", "getItem", "ACCESS_TOKEN", "authRequest", "clone", "headers", "set", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { AuthService } from '@app/core/services/auth.service';\r\n//ng generate interceptor my-interceptor --skip-tests\r\n\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, <PERSON>ttpHandler } from '@angular/common/http';\r\nimport { Auth } from '../enums/auth.enum';\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n    authService = inject(AuthService);\r\n    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>) {\r\n        // Skip adding Authorization header for auth endpoints\r\n        const isAuthEndpoint = request.url.includes('/api/auth/');\r\n        const token = localStorage.getItem(Auth.ACCESS_TOKEN);\r\n\r\n        if (!isAuthEndpoint && token) {\r\n            const authRequest = request.clone({\r\n                headers: request.headers.set('Authorization', `Bearer ${token}`),\r\n            });\r\n            return next.handle(authRequest);\r\n        }\r\n\r\n        return next.handle(request);\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,iCAAiC;AAC7D;AAEA,SAASC,MAAM,QAAoB,eAAe;AAElD,SAASC,IAAI,QAAQ,oBAAoB;;AAGzC,OAAM,MAAOC,eAAe;EAD5BC,YAAA;IAEI,KAAAC,WAAW,GAAGJ,MAAM,CAACD,WAAW,CAAC;;EACjCM,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD;IACA,MAAMC,cAAc,GAAGF,OAAO,CAACG,GAAG,CAACC,QAAQ,CAAC,YAAY,CAAC;IACzD,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACZ,IAAI,CAACa,YAAY,CAAC;IAErD,IAAI,CAACN,cAAc,IAAIG,KAAK,EAAE;MAC1B,MAAMI,WAAW,GAAGT,OAAO,CAACU,KAAK,CAAC;QAC9BC,OAAO,EAAEX,OAAO,CAACW,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUP,KAAK,EAAE;OAClE,CAAC;MACF,OAAOJ,IAAI,CAACY,MAAM,CAACJ,WAAW,CAAC;;IAGnC,OAAOR,IAAI,CAACY,MAAM,CAACb,OAAO,CAAC;EAC/B;EAAC,QAAAc,CAAA,G;qBAfQlB,eAAe;EAAA;EAAA,QAAAmB,EAAA,G;WAAfnB,eAAe;IAAAoB,OAAA,EAAfpB,eAAe,CAAAqB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}