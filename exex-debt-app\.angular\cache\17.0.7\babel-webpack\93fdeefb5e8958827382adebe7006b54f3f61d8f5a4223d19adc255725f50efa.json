{"ast": null, "code": "import { Auth } from '../enums/auth.enum';\n/**\n * Utility class for managing localStorage with app-specific keys\n * This helps avoid conflicts with other applications\n */\nexport class StorageUtil {\n  // Authentication related storage keys\n  static #_ = this.AUTH_KEYS = Auth; // App-specific storage keys for other data\n  static #_2 = this.APP_KEYS = {\n    THEME: 'debt_theme',\n    COLOR_SCHEME: 'debt_color_scheme',\n    SCALE: 'debt_scale',\n    LANGUAGE: 'debt_language'\n  };\n  /**\n   * Get authentication token from storage\n   */\n  static getAccessToken() {\n    return localStorage.getItem(Auth.ACCESS_TOKEN);\n  }\n  /**\n   * Get refresh token from storage\n   */\n  static getRefreshToken() {\n    return localStorage.getItem(Auth.REFRESH_TOKEN);\n  }\n  /**\n   * Set authentication tokens\n   */\n  static setTokens(accessToken, refreshToken) {\n    localStorage.setItem(Auth.ACCESS_TOKEN, accessToken);\n    localStorage.setItem(Auth.REFRESH_TOKEN, refreshToken);\n  }\n  /**\n   * Clear all authentication tokens\n   */\n  static clearAuthTokens() {\n    localStorage.removeItem(Auth.ACCESS_TOKEN);\n    localStorage.removeItem(Auth.REFRESH_TOKEN);\n  }\n  /**\n   * Check if user has valid tokens\n   */\n  static hasValidTokens() {\n    return !!(this.getAccessToken() && this.getRefreshToken());\n  }\n  /**\n   * Get app setting from storage\n   */\n  static getAppSetting(key) {\n    return localStorage.getItem(StorageUtil.APP_KEYS[key]);\n  }\n  /**\n   * Set app setting to storage\n   */\n  static setAppSetting(key, value) {\n    localStorage.setItem(StorageUtil.APP_KEYS[key], value);\n  }\n  /**\n   * Clear all app-specific data from storage\n   */\n  static clearAllAppData() {\n    // Clear auth tokens\n    this.clearAuthTokens();\n    // Clear app settings\n    Object.values(StorageUtil.APP_KEYS).forEach(key => {\n      localStorage.removeItem(key);\n    });\n  }\n  /**\n   * Debug method to log all app-related storage keys\n   */\n  static debugStorage() {\n    console.group('🔍 Debt App Storage Debug');\n    console.log('Access Token:', this.getAccessToken() ? '✅ Present' : '❌ Missing');\n    console.log('Refresh Token:', this.getRefreshToken() ? '✅ Present' : '❌ Missing');\n    Object.entries(StorageUtil.APP_KEYS).forEach(([name, key]) => {\n      const value = localStorage.getItem(key);\n      console.log(`${name}:`, value || '❌ Not set');\n    });\n    console.groupEnd();\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "StorageUtil", "_", "AUTH_KEYS", "_2", "APP_KEYS", "THEME", "COLOR_SCHEME", "SCALE", "LANGUAGE", "getAccessToken", "localStorage", "getItem", "ACCESS_TOKEN", "getRefreshToken", "REFRESH_TOKEN", "setTokens", "accessToken", "refreshToken", "setItem", "clearAuthTokens", "removeItem", "hasValidTokens", "getAppSetting", "key", "setAppSetting", "value", "clearAllAppData", "Object", "values", "for<PERSON>ach", "debugStorage", "console", "group", "log", "entries", "name", "groupEnd"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\utils\\storage.util.ts"], "sourcesContent": ["import { Auth } from '../enums/auth.enum';\n\n/**\n * Utility class for managing localStorage with app-specific keys\n * This helps avoid conflicts with other applications\n */\nexport class StorageUtil {\n    // Authentication related storage keys\n    static readonly AUTH_KEYS = Auth;\n\n    // App-specific storage keys for other data\n    static readonly APP_KEYS = {\n        THEME: 'debt_theme',\n        COLOR_SCHEME: 'debt_color_scheme',\n        SCALE: 'debt_scale',\n        LANGUAGE: 'debt_language'\n    } as const;\n\n    /**\n     * Get authentication token from storage\n     */\n    static getAccessToken(): string | null {\n        return localStorage.getItem(Auth.ACCESS_TOKEN);\n    }\n\n    /**\n     * Get refresh token from storage\n     */\n    static getRefreshToken(): string | null {\n        return localStorage.getItem(Auth.REFRESH_TOKEN);\n    }\n\n    /**\n     * Set authentication tokens\n     */\n    static setTokens(accessToken: string, refreshToken: string): void {\n        localStorage.setItem(Auth.ACCESS_TOKEN, accessToken);\n        localStorage.setItem(Auth.REFRESH_TOKEN, refreshToken);\n    }\n\n    /**\n     * Clear all authentication tokens\n     */\n    static clearAuthTokens(): void {\n        localStorage.removeItem(Auth.ACCESS_TOKEN);\n        localStorage.removeItem(Auth.REFRESH_TOKEN);\n    }\n\n    /**\n     * Check if user has valid tokens\n     */\n    static hasValidTokens(): boolean {\n        return !!(this.getAccessToken() && this.getRefreshToken());\n    }\n\n    /**\n     * Get app setting from storage\n     */\n    static getAppSetting(key: keyof typeof StorageUtil.APP_KEYS): string | null {\n        return localStorage.getItem(StorageUtil.APP_KEYS[key]);\n    }\n\n    /**\n     * Set app setting to storage\n     */\n    static setAppSetting(key: keyof typeof StorageUtil.APP_KEYS, value: string): void {\n        localStorage.setItem(StorageUtil.APP_KEYS[key], value);\n    }\n\n    /**\n     * Clear all app-specific data from storage\n     */\n    static clearAllAppData(): void {\n        // Clear auth tokens\n        this.clearAuthTokens();\n        \n        // Clear app settings\n        Object.values(StorageUtil.APP_KEYS).forEach(key => {\n            localStorage.removeItem(key);\n        });\n    }\n\n    /**\n     * Debug method to log all app-related storage keys\n     */\n    static debugStorage(): void {\n        console.group('🔍 Debt App Storage Debug');\n        console.log('Access Token:', this.getAccessToken() ? '✅ Present' : '❌ Missing');\n        console.log('Refresh Token:', this.getRefreshToken() ? '✅ Present' : '❌ Missing');\n        \n        Object.entries(StorageUtil.APP_KEYS).forEach(([name, key]) => {\n            const value = localStorage.getItem(key);\n            console.log(`${name}:`, value || '❌ Not set');\n        });\n        console.groupEnd();\n    }\n}\n"], "mappings": "AAAA,SAASA,IAAI,QAAQ,oBAAoB;AAEzC;;;;AAIA,OAAM,MAAOC,WAAW;EACpB;EAAA,QAAAC,CAAA,GACgB,KAAAC,SAAS,GAAGH,IAAI,EAEhC;EAAA,QAAAI,EAAA,GACgB,KAAAC,QAAQ,GAAG;IACvBC,KAAK,EAAE,YAAY;IACnBC,YAAY,EAAE,mBAAmB;IACjCC,KAAK,EAAE,YAAY;IACnBC,QAAQ,EAAE;GACJ;EAEV;;;EAGA,OAAOC,cAAcA,CAAA;IACjB,OAAOC,YAAY,CAACC,OAAO,CAACZ,IAAI,CAACa,YAAY,CAAC;EAClD;EAEA;;;EAGA,OAAOC,eAAeA,CAAA;IAClB,OAAOH,YAAY,CAACC,OAAO,CAACZ,IAAI,CAACe,aAAa,CAAC;EACnD;EAEA;;;EAGA,OAAOC,SAASA,CAACC,WAAmB,EAAEC,YAAoB;IACtDP,YAAY,CAACQ,OAAO,CAACnB,IAAI,CAACa,YAAY,EAAEI,WAAW,CAAC;IACpDN,YAAY,CAACQ,OAAO,CAACnB,IAAI,CAACe,aAAa,EAAEG,YAAY,CAAC;EAC1D;EAEA;;;EAGA,OAAOE,eAAeA,CAAA;IAClBT,YAAY,CAACU,UAAU,CAACrB,IAAI,CAACa,YAAY,CAAC;IAC1CF,YAAY,CAACU,UAAU,CAACrB,IAAI,CAACe,aAAa,CAAC;EAC/C;EAEA;;;EAGA,OAAOO,cAAcA,CAAA;IACjB,OAAO,CAAC,EAAE,IAAI,CAACZ,cAAc,EAAE,IAAI,IAAI,CAACI,eAAe,EAAE,CAAC;EAC9D;EAEA;;;EAGA,OAAOS,aAAaA,CAACC,GAAsC;IACvD,OAAOb,YAAY,CAACC,OAAO,CAACX,WAAW,CAACI,QAAQ,CAACmB,GAAG,CAAC,CAAC;EAC1D;EAEA;;;EAGA,OAAOC,aAAaA,CAACD,GAAsC,EAAEE,KAAa;IACtEf,YAAY,CAACQ,OAAO,CAAClB,WAAW,CAACI,QAAQ,CAACmB,GAAG,CAAC,EAAEE,KAAK,CAAC;EAC1D;EAEA;;;EAGA,OAAOC,eAAeA,CAAA;IAClB;IACA,IAAI,CAACP,eAAe,EAAE;IAEtB;IACAQ,MAAM,CAACC,MAAM,CAAC5B,WAAW,CAACI,QAAQ,CAAC,CAACyB,OAAO,CAACN,GAAG,IAAG;MAC9Cb,YAAY,CAACU,UAAU,CAACG,GAAG,CAAC;IAChC,CAAC,CAAC;EACN;EAEA;;;EAGA,OAAOO,YAAYA,CAAA;IACfC,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAC;IAC1CD,OAAO,CAACE,GAAG,CAAC,eAAe,EAAE,IAAI,CAACxB,cAAc,EAAE,GAAG,WAAW,GAAG,WAAW,CAAC;IAC/EsB,OAAO,CAACE,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACpB,eAAe,EAAE,GAAG,WAAW,GAAG,WAAW,CAAC;IAEjFc,MAAM,CAACO,OAAO,CAAClC,WAAW,CAACI,QAAQ,CAAC,CAACyB,OAAO,CAAC,CAAC,CAACM,IAAI,EAAEZ,GAAG,CAAC,KAAI;MACzD,MAAME,KAAK,GAAGf,YAAY,CAACC,OAAO,CAACY,GAAG,CAAC;MACvCQ,OAAO,CAACE,GAAG,CAAC,GAAGE,IAAI,GAAG,EAAEV,KAAK,IAAI,WAAW,CAAC;IACjD,CAAC,CAAC;IACFM,OAAO,CAACK,QAAQ,EAAE;EACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}