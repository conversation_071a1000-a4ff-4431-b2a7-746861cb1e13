{"ast": null, "code": "import { AuthService } from '@app/core/services/auth.service';\n//ng generate interceptor my-interceptor --skip-tests\nimport { inject } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class AuthInterceptor {\n  constructor() {\n    this.authService = inject(AuthService);\n  }\n  intercept(request, next) {\n    // Skip adding Authorization header for login and refresh endpoints only\n    const isLoginEndpoint = request.url.includes('/api/auth/login');\n    const isRefreshEndpoint = request.url.includes('/api/auth/refresh');\n    const token = localStorage.getItem(Auth.ACCESS_TOKEN);\n    if (!isLoginEndpoint && !isRefreshEndpoint && token) {\n      const authRequest = request.clone({\n        headers: request.headers.set('Authorization', `Bearer ${token}`)\n      });\n      return next.handle(authRequest);\n    }\n    return next.handle(request);\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(t) {\n    return new (t || AuthInterceptor)();\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["AuthService", "inject", "AuthInterceptor", "constructor", "authService", "intercept", "request", "next", "isLoginEndpoint", "url", "includes", "isRefreshEndpoint", "token", "localStorage", "getItem", "<PERSON><PERSON>", "ACCESS_TOKEN", "authRequest", "clone", "headers", "set", "handle", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\auth.interceptor.ts"], "sourcesContent": ["import { AuthService } from '@app/core/services/auth.service';\r\n//ng generate interceptor my-interceptor --skip-tests\r\n\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, <PERSON>ttpHandler } from '@angular/common/http';\r\nimport { StorageUtil } from '../utils/storage.util';\r\n\r\n@Injectable()\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n    authService = inject(AuthService);\r\n    intercept(request: HttpRequest<any>, next: HttpHandler) {\r\n        // Skip adding Authorization header for login and refresh endpoints only\r\n        const isLoginEndpoint = request.url.includes('/api/auth/login');\r\n        const isRefreshEndpoint = request.url.includes('/api/auth/refresh');\r\n        const token = localStorage.getItem(Auth.ACCESS_TOKEN);\r\n\r\n        if (!isLoginEndpoint && !isRefreshEndpoint && token) {\r\n            const authRequest = request.clone({\r\n                headers: request.headers.set('Authorization', `Bearer ${token}`),\r\n            });\r\n            return next.handle(authRequest);\r\n        }\r\n\r\n        return next.handle(request);\r\n    }\r\n}\r\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,iCAAiC;AAC7D;AAEA,SAASC,MAAM,QAAoB,eAAe;;AAKlD,OAAM,MAAOC,eAAe;EAD5BC,YAAA;IAEI,KAAAC,WAAW,GAAGH,MAAM,CAACD,WAAW,CAAC;;EACjCK,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD;IACA,MAAMC,eAAe,GAAGF,OAAO,CAACG,GAAG,CAACC,QAAQ,CAAC,iBAAiB,CAAC;IAC/D,MAAMC,iBAAiB,GAAGL,OAAO,CAACG,GAAG,CAACC,QAAQ,CAAC,mBAAmB,CAAC;IACnE,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAACC,IAAI,CAACC,YAAY,CAAC;IAErD,IAAI,CAACR,eAAe,IAAI,CAACG,iBAAiB,IAAIC,KAAK,EAAE;MACjD,MAAMK,WAAW,GAAGX,OAAO,CAACY,KAAK,CAAC;QAC9BC,OAAO,EAAEb,OAAO,CAACa,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,UAAUR,KAAK,EAAE;OAClE,CAAC;MACF,OAAOL,IAAI,CAACc,MAAM,CAACJ,WAAW,CAAC;;IAGnC,OAAOV,IAAI,CAACc,MAAM,CAACf,OAAO,CAAC;EAC/B;EAAC,QAAAgB,CAAA,G;qBAhBQpB,eAAe;EAAA;EAAA,QAAAqB,EAAA,G;WAAfrB,eAAe;IAAAsB,OAAA,EAAftB,eAAe,CAAAuB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}