{"ast": null, "code": "import { catchError, switchMap } from 'rxjs/operators';\nimport { throwError } from 'rxjs';\nimport { Auth } from '../enums/auth.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nexport class JwtRefreshInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(request, next) {\n    return next.handle(request).pipe(catchError(error => {\n      if (error.status === 401) {\n        // Token expired or unauthorized; attempt to refresh it\n        return this.authService.refreshToken().pipe(switchMap(response => {\n          // Retry the original request with the new token\n          const updatedRequest = request.clone({\n            setHeaders: {\n              Authorization: `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`\n            }\n          });\n          return next.handle(updatedRequest);\n        }), catchError(() => {\n          // Refresh token failed; log out the user immediately\n          this.authService.logoutImmediate();\n          return throwError(() => new Error('Token refresh failed'));\n        }));\n      }\n      return throwError(error);\n    }));\n  }\n  static #_ = this.ɵfac = function JwtRefreshInterceptor_Factory(t) {\n    return new (t || JwtRefreshInterceptor)(i0.ɵɵinject(i1.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: JwtRefreshInterceptor,\n    factory: JwtRefreshInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["catchError", "switchMap", "throwError", "<PERSON><PERSON>", "JwtRefreshInterceptor", "constructor", "authService", "intercept", "request", "next", "handle", "pipe", "error", "status", "refreshToken", "response", "updatedRequest", "clone", "setHeaders", "Authorization", "localStorage", "getItem", "ACCESS_TOKEN", "logoutImmediate", "Error", "_", "i0", "ɵɵinject", "i1", "AuthService", "_2", "factory", "ɵfac"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\interceptors\\jwt-refresh.interceptor.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';\r\nimport { catchError, switchMap } from 'rxjs/operators';\r\nimport { throwError } from 'rxjs';\r\nimport { Auth } from '../enums/auth.enum';\r\nimport { AuthService } from '../services/auth.service';\r\n\r\n@Injectable()\r\nexport class JwtRefreshInterceptor implements HttpInterceptor {\r\n    constructor(private authService: AuthService) {}\r\n\r\n    intercept(request: HttpRequest<any>, next: HttpHandler) {\r\n        return next.handle(request).pipe(\r\n            catchError((error: HttpErrorResponse) => {\r\n                if (error.status === 401) {\r\n                    // Token expired or unauthorized; attempt to refresh it\r\n                    return this.authService.refreshToken().pipe(\r\n                        switchMap((response) => {\r\n                            // Retry the original request with the new token\r\n                            const updatedRequest = request.clone({\r\n                                setHeaders: {\r\n                                    Authorization: `Bearer ${localStorage.getItem(Auth.ACCESS_TOKEN)}`,\r\n                                },\r\n                            });\r\n                            return next.handle(updatedRequest);\r\n                        }),\r\n                        catchError(() => {\r\n                            // Refresh token failed; log out the user immediately\r\n                            this.authService.logoutImmediate();\r\n                            return throwError(() => new Error('Token refresh failed'));\r\n                        }),\r\n                    );\r\n                }\r\n                return throwError(error);\r\n            }),\r\n        );\r\n    }\r\n}\r\n"], "mappings": "AAEA,SAASA,UAAU,EAAEC,SAAS,QAAQ,gBAAgB;AACtD,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,IAAI,QAAQ,oBAAoB;;;AAIzC,OAAM,MAAOC,qBAAqB;EAC9BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE/CC,SAASA,CAACC,OAAyB,EAAEC,IAAiB;IAClD,OAAOA,IAAI,CAACC,MAAM,CAACF,OAAO,CAAC,CAACG,IAAI,CAC5BX,UAAU,CAAEY,KAAwB,IAAI;MACpC,IAAIA,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QACtB;QACA,OAAO,IAAI,CAACP,WAAW,CAACQ,YAAY,EAAE,CAACH,IAAI,CACvCV,SAAS,CAAEc,QAAQ,IAAI;UACnB;UACA,MAAMC,cAAc,GAAGR,OAAO,CAACS,KAAK,CAAC;YACjCC,UAAU,EAAE;cACRC,aAAa,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAClB,IAAI,CAACmB,YAAY,CAAC;;WAEvE,CAAC;UACF,OAAOb,IAAI,CAACC,MAAM,CAACM,cAAc,CAAC;QACtC,CAAC,CAAC,EACFhB,UAAU,CAAC,MAAK;UACZ;UACA,IAAI,CAACM,WAAW,CAACiB,eAAe,EAAE;UAClC,OAAOrB,UAAU,CAAC,MAAM,IAAIsB,KAAK,CAAC,sBAAsB,CAAC,CAAC;QAC9D,CAAC,CAAC,CACL;;MAEL,OAAOtB,UAAU,CAACU,KAAK,CAAC;IAC5B,CAAC,CAAC,CACL;EACL;EAAC,QAAAa,CAAA,G;qBA5BQrB,qBAAqB,EAAAsB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAArB1B,qBAAqB;IAAA2B,OAAA,EAArB3B,qBAAqB,CAAA4B;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}