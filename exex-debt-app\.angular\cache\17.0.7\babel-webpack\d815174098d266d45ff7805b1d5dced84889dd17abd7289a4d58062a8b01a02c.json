{"ast": null, "code": "import { inject } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { Observable, tap, catchError, throwError } from 'rxjs';\nimport { Auth } from '../enums/auth.enum';\nimport { Path } from '../enums/path.enum';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"ngx-spinner\";\nexport class AuthService {\n  constructor(http, spinner) {\n    this.http = http;\n    this.spinner = spinner;\n    this.redirectTo = inject(Router);\n    const token = localStorage.getItem(Auth.ACCESS_TOKEN);\n    if (token) {\n      this.jwtToken = token;\n    }\n  }\n  refreshToken() {\n    const refreshToken = localStorage.getItem(Auth.REFRESH_TOKEN);\n    if (!refreshToken) {\n      return throwError(() => new Error('No refresh token available'));\n    }\n    return this.http.post('api/auth/refresh', {\n      refreshToken\n    }).pipe(tap(response => {\n      if (response.success && response.data) {\n        localStorage.setItem(Auth.ACCESS_TOKEN, response.data.accessToken);\n        localStorage.setItem(Auth.REFRESH_TOKEN, response.data.refreshToken);\n        this.jwtToken = response.data.accessToken;\n      }\n    }), catchError(error => {\n      this.logoutImmediate();\n      return throwError(() => error);\n    }));\n  }\n  isAuthenticated() {\n    // Check both in-memory token and localStorage\n    const token = this.jwtToken || localStorage.getItem(Auth.ACCESS_TOKEN);\n    if (!token) {\n      return false;\n    }\n    // Update in-memory token if it's missing but exists in localStorage\n    if (!this.jwtToken && token) {\n      this.jwtToken = token;\n    }\n    return true;\n  }\n  redirectToDashboard() {\n    this.jwtToken = localStorage.getItem(Auth.ACCESS_TOKEN);\n    this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\n  }\n  login(credentials) {\n    this.spinner.show();\n    return this.http.post('api/auth/login', credentials).pipe(tap(response => {\n      if (response.success && response.data) {\n        this.jwtToken = response.data.accessToken;\n        localStorage.setItem(Auth.ACCESS_TOKEN, response.data.accessToken);\n        localStorage.setItem(Auth.REFRESH_TOKEN, response.data.refreshToken);\n        this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\n      }\n      this.spinner.hide();\n    }), catchError(error => {\n      this.spinner.hide();\n      return throwError(() => error);\n    }));\n  }\n  logout() {\n    console.log('Logout initiated');\n    return this.http.post('api/auth/logout', {}).pipe(tap(response => {\n      console.log('Logout API success:', response);\n      // Clear tokens regardless of API response\n      this.clearTokensAndRedirect();\n    }), catchError(error => {\n      console.log('Logout API failed:', error);\n      // Clear tokens even if logout API fails\n      this.clearTokensAndRedirect();\n      // Don't throw error, just complete successfully\n      return new Observable(subscriber => {\n        subscriber.next({\n          success: true,\n          message: 'Đăng xuất thành công (local)',\n          data: 'Logged out locally'\n        });\n        subscriber.complete();\n      });\n    }));\n  }\n  clearTokensAndRedirect() {\n    console.log('Clearing tokens and redirecting');\n    this.jwtToken = null;\n    localStorage.removeItem(Auth.ACCESS_TOKEN);\n    localStorage.removeItem(Auth.REFRESH_TOKEN);\n    this.redirectTo.navigate([Path.AUTH_LOGIN]);\n  }\n  // Method for immediate logout without API call (for error scenarios)\n  logoutImmediate() {\n    this.jwtToken = null;\n    localStorage.removeItem(Auth.ACCESS_TOKEN);\n    localStorage.removeItem(Auth.REFRESH_TOKEN);\n    this.redirectTo.navigate([Path.AUTH_LOGIN]);\n  }\n  mockUser() {\n    return this.http.get('https://jsonplaceholder.typicode.com/users');\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.NgxSpinnerService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "Router", "Observable", "tap", "catchError", "throwError", "<PERSON><PERSON>", "Path", "AuthService", "constructor", "http", "spinner", "redirectTo", "token", "localStorage", "getItem", "ACCESS_TOKEN", "jwtToken", "refreshToken", "REFRESH_TOKEN", "Error", "post", "pipe", "response", "success", "data", "setItem", "accessToken", "error", "logoutImmediate", "isAuthenticated", "redirectToDashboard", "navigate", "DASHBOARD_CUSTOMER", "login", "credentials", "show", "hide", "logout", "console", "log", "clearTokensAndRedirect", "subscriber", "next", "message", "complete", "removeItem", "AUTH_LOGIN", "mockUser", "get", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "NgxSpinnerService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { inject, Injectable } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { NgxSpinnerService } from 'ngx-spinner';\r\nimport { Observable, tap, catchError, throwError } from 'rxjs';\r\nimport { Auth } from '../enums/auth.enum';\r\nimport { Path } from '../enums/path.enum';\r\nimport { LoginRequest, LoginResponse, LogoutResponse, RefreshTokenResponse } from '../models/auth.model';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class AuthService {\r\n    private jwtToken;\r\n    redirectTo = inject(Router);\r\n\r\n    constructor(\r\n        private http: HttpClient,\r\n        private spinner: NgxSpinnerService,\r\n    ) {\r\n        const token: any = localStorage.getItem(Auth.ACCESS_TOKEN);\r\n        if (token) {\r\n            this.jwtToken = token;\r\n        }\r\n    }\r\n\r\n    refreshToken(): Observable<RefreshTokenResponse> {\r\n        const refreshToken = localStorage.getItem(Auth.REFRESH_TOKEN);\r\n        if (!refreshToken) {\r\n            return throwError(() => new Error('No refresh token available'));\r\n        }\r\n\r\n        return this.http.post<RefreshTokenResponse>('api/auth/refresh', { refreshToken }).pipe(\r\n            tap((response) => {\r\n                if (response.success && response.data) {\r\n                    localStorage.setItem(Auth.ACCESS_TOKEN, response.data.accessToken);\r\n                    localStorage.setItem(Auth.REFRESH_TOKEN, response.data.refreshToken);\r\n                    this.jwtToken = response.data.accessToken;\r\n                }\r\n            }),\r\n            catchError((error) => {\r\n                this.logoutImmediate();\r\n                return throwError(() => error);\r\n            })\r\n        );\r\n    }\r\n\r\n    public isAuthenticated(): boolean {\r\n        // Check both in-memory token and localStorage\r\n        const token = this.jwtToken || localStorage.getItem(Auth.ACCESS_TOKEN);\r\n        if (!token) {\r\n            return false;\r\n        }\r\n\r\n        // Update in-memory token if it's missing but exists in localStorage\r\n        if (!this.jwtToken && token) {\r\n            this.jwtToken = token;\r\n        }\r\n\r\n        return true;\r\n    }\r\n\r\n    redirectToDashboard() {\r\n        this.jwtToken = localStorage.getItem(Auth.ACCESS_TOKEN);\r\n        this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\r\n    }\r\n\r\n    login(credentials: LoginRequest): Observable<LoginResponse> {\r\n        this.spinner.show();\r\n\r\n        return this.http.post<LoginResponse>('api/auth/login', credentials).pipe(\r\n            tap((response) => {\r\n                if (response.success && response.data) {\r\n                    this.jwtToken = response.data.accessToken;\r\n                    localStorage.setItem(Auth.ACCESS_TOKEN, response.data.accessToken);\r\n                    localStorage.setItem(Auth.REFRESH_TOKEN, response.data.refreshToken);\r\n                    this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);\r\n                }\r\n                this.spinner.hide();\r\n            }),\r\n            catchError((error) => {\r\n                this.spinner.hide();\r\n                return throwError(() => error);\r\n            })\r\n        );\r\n    }\r\n\r\n    logout(): Observable<LogoutResponse> {\r\n        console.log('Logout initiated');\r\n\r\n        return this.http.post<LogoutResponse>('api/auth/logout', {}).pipe(\r\n            tap((response) => {\r\n                console.log('Logout API success:', response);\r\n                // Clear tokens regardless of API response\r\n                this.clearTokensAndRedirect();\r\n            }),\r\n            catchError((error) => {\r\n                console.log('Logout API failed:', error);\r\n                // Clear tokens even if logout API fails\r\n                this.clearTokensAndRedirect();\r\n                // Don't throw error, just complete successfully\r\n                return new Observable<LogoutResponse>(subscriber => {\r\n                    subscriber.next({\r\n                        success: true,\r\n                        message: 'Đăng xuất thành công (local)',\r\n                        data: 'Logged out locally'\r\n                    });\r\n                    subscriber.complete();\r\n                });\r\n            })\r\n        );\r\n    }\r\n\r\n    private clearTokensAndRedirect(): void {\r\n        console.log('Clearing tokens and redirecting');\r\n        this.jwtToken = null;\r\n        localStorage.removeItem(Auth.ACCESS_TOKEN);\r\n        localStorage.removeItem(Auth.REFRESH_TOKEN);\r\n        this.redirectTo.navigate([Path.AUTH_LOGIN]);\r\n    }\r\n\r\n    // Method for immediate logout without API call (for error scenarios)\r\n    logoutImmediate(): void {\r\n        this.jwtToken = null;\r\n        localStorage.removeItem(Auth.ACCESS_TOKEN);\r\n        localStorage.removeItem(Auth.REFRESH_TOKEN);\r\n        this.redirectTo.navigate([Path.AUTH_LOGIN]);\r\n    }\r\n\r\n    mockUser(): Observable<any> {\r\n        return this.http.get('https://jsonplaceholder.typicode.com/users');\r\n    }\r\n}\r\n"], "mappings": "AACA,SAASA,MAAM,QAAoB,eAAe;AAClD,SAASC,MAAM,QAAQ,iBAAiB;AAExC,SAASC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,UAAU,QAAQ,MAAM;AAC9D,SAASC,IAAI,QAAQ,oBAAoB;AACzC,SAASC,IAAI,QAAQ,oBAAoB;;;;AAMzC,OAAM,MAAOC,WAAW;EAIpBC,YACYC,IAAgB,EAChBC,OAA0B;IAD1B,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,OAAO,GAAPA,OAAO;IAJnB,KAAAC,UAAU,GAAGZ,MAAM,CAACC,MAAM,CAAC;IAMvB,MAAMY,KAAK,GAAQC,YAAY,CAACC,OAAO,CAACT,IAAI,CAACU,YAAY,CAAC;IAC1D,IAAIH,KAAK,EAAE;MACP,IAAI,CAACI,QAAQ,GAAGJ,KAAK;;EAE7B;EAEAK,YAAYA,CAAA;IACR,MAAMA,YAAY,GAAGJ,YAAY,CAACC,OAAO,CAACT,IAAI,CAACa,aAAa,CAAC;IAC7D,IAAI,CAACD,YAAY,EAAE;MACf,OAAOb,UAAU,CAAC,MAAM,IAAIe,KAAK,CAAC,4BAA4B,CAAC,CAAC;;IAGpE,OAAO,IAAI,CAACV,IAAI,CAACW,IAAI,CAAuB,kBAAkB,EAAE;MAAEH;IAAY,CAAE,CAAC,CAACI,IAAI,CAClFnB,GAAG,CAAEoB,QAAQ,IAAI;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACnCX,YAAY,CAACY,OAAO,CAACpB,IAAI,CAACU,YAAY,EAAEO,QAAQ,CAACE,IAAI,CAACE,WAAW,CAAC;QAClEb,YAAY,CAACY,OAAO,CAACpB,IAAI,CAACa,aAAa,EAAEI,QAAQ,CAACE,IAAI,CAACP,YAAY,CAAC;QACpE,IAAI,CAACD,QAAQ,GAAGM,QAAQ,CAACE,IAAI,CAACE,WAAW;;IAEjD,CAAC,CAAC,EACFvB,UAAU,CAAEwB,KAAK,IAAI;MACjB,IAAI,CAACC,eAAe,EAAE;MACtB,OAAOxB,UAAU,CAAC,MAAMuB,KAAK,CAAC;IAClC,CAAC,CAAC,CACL;EACL;EAEOE,eAAeA,CAAA;IAClB;IACA,MAAMjB,KAAK,GAAG,IAAI,CAACI,QAAQ,IAAIH,YAAY,CAACC,OAAO,CAACT,IAAI,CAACU,YAAY,CAAC;IACtE,IAAI,CAACH,KAAK,EAAE;MACR,OAAO,KAAK;;IAGhB;IACA,IAAI,CAAC,IAAI,CAACI,QAAQ,IAAIJ,KAAK,EAAE;MACzB,IAAI,CAACI,QAAQ,GAAGJ,KAAK;;IAGzB,OAAO,IAAI;EACf;EAEAkB,mBAAmBA,CAAA;IACf,IAAI,CAACd,QAAQ,GAAGH,YAAY,CAACC,OAAO,CAACT,IAAI,CAACU,YAAY,CAAC;IACvD,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACL,UAAU,CAACoB,QAAQ,CAAC,CAACzB,IAAI,CAAC0B,kBAAkB,CAAC,CAAC;EACxE;EAEAC,KAAKA,CAACC,WAAyB;IAC3B,IAAI,CAACxB,OAAO,CAACyB,IAAI,EAAE;IAEnB,OAAO,IAAI,CAAC1B,IAAI,CAACW,IAAI,CAAgB,gBAAgB,EAAEc,WAAW,CAAC,CAACb,IAAI,CACpEnB,GAAG,CAAEoB,QAAQ,IAAI;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACnC,IAAI,CAACR,QAAQ,GAAGM,QAAQ,CAACE,IAAI,CAACE,WAAW;QACzCb,YAAY,CAACY,OAAO,CAACpB,IAAI,CAACU,YAAY,EAAEO,QAAQ,CAACE,IAAI,CAACE,WAAW,CAAC;QAClEb,YAAY,CAACY,OAAO,CAACpB,IAAI,CAACa,aAAa,EAAEI,QAAQ,CAACE,IAAI,CAACP,YAAY,CAAC;QACpE,IAAI,CAACN,UAAU,CAACoB,QAAQ,CAAC,CAACzB,IAAI,CAAC0B,kBAAkB,CAAC,CAAC;;MAEvD,IAAI,CAACtB,OAAO,CAAC0B,IAAI,EAAE;IACvB,CAAC,CAAC,EACFjC,UAAU,CAAEwB,KAAK,IAAI;MACjB,IAAI,CAACjB,OAAO,CAAC0B,IAAI,EAAE;MACnB,OAAOhC,UAAU,CAAC,MAAMuB,KAAK,CAAC;IAClC,CAAC,CAAC,CACL;EACL;EAEAU,MAAMA,CAAA;IACFC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAE/B,OAAO,IAAI,CAAC9B,IAAI,CAACW,IAAI,CAAiB,iBAAiB,EAAE,EAAE,CAAC,CAACC,IAAI,CAC7DnB,GAAG,CAAEoB,QAAQ,IAAI;MACbgB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEjB,QAAQ,CAAC;MAC5C;MACA,IAAI,CAACkB,sBAAsB,EAAE;IACjC,CAAC,CAAC,EACFrC,UAAU,CAAEwB,KAAK,IAAI;MACjBW,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEZ,KAAK,CAAC;MACxC;MACA,IAAI,CAACa,sBAAsB,EAAE;MAC7B;MACA,OAAO,IAAIvC,UAAU,CAAiBwC,UAAU,IAAG;QAC/CA,UAAU,CAACC,IAAI,CAAC;UACZnB,OAAO,EAAE,IAAI;UACboB,OAAO,EAAE,8BAA8B;UACvCnB,IAAI,EAAE;SACT,CAAC;QACFiB,UAAU,CAACG,QAAQ,EAAE;MACzB,CAAC,CAAC;IACN,CAAC,CAAC,CACL;EACL;EAEQJ,sBAAsBA,CAAA;IAC1BF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C,IAAI,CAACvB,QAAQ,GAAG,IAAI;IACpBH,YAAY,CAACgC,UAAU,CAACxC,IAAI,CAACU,YAAY,CAAC;IAC1CF,YAAY,CAACgC,UAAU,CAACxC,IAAI,CAACa,aAAa,CAAC;IAC3C,IAAI,CAACP,UAAU,CAACoB,QAAQ,CAAC,CAACzB,IAAI,CAACwC,UAAU,CAAC,CAAC;EAC/C;EAEA;EACAlB,eAAeA,CAAA;IACX,IAAI,CAACZ,QAAQ,GAAG,IAAI;IACpBH,YAAY,CAACgC,UAAU,CAACxC,IAAI,CAACU,YAAY,CAAC;IAC1CF,YAAY,CAACgC,UAAU,CAACxC,IAAI,CAACa,aAAa,CAAC;IAC3C,IAAI,CAACP,UAAU,CAACoB,QAAQ,CAAC,CAACzB,IAAI,CAACwC,UAAU,CAAC,CAAC;EAC/C;EAEAC,QAAQA,CAAA;IACJ,OAAO,IAAI,CAACtC,IAAI,CAACuC,GAAG,CAAC,4CAA4C,CAAC;EACtE;EAAC,QAAAC,CAAA,G;qBAvHQ1C,WAAW,EAAA2C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXjD,WAAW;IAAAkD,OAAA,EAAXlD,WAAW,CAAAmD,IAAA;IAAAC,UAAA,EAFR;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}