{"ast": null, "code": "import { StorageUtil } from '@app/core/utils/storage.util';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/app.layout.service\";\nimport * as i2 from \"@app/core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nfunction LoginComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.errorMessage, \" \");\n  }\n}\nfunction LoginComponent_span_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"\\u0110\\u0103ng nh\\u1EADp\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_span_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"i\", 36);\n    i0.ɵɵtext(2, \" \\u0110ang \\u0111\\u0103ng nh\\u1EADp... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(layoutService, authService) {\n    this.layoutService = layoutService;\n    this.authService = authService;\n    this.valCheck = ['remember'];\n    this.username = '';\n    this.password = '';\n    this.errorMessage = '';\n    this.isLoading = false;\n    // Only redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      console.log('User already authenticated, redirecting to dashboard');\n      this.authService.redirectToDashboard();\n    }\n  }\n  login() {\n    // Prevent multiple submissions\n    if (this.isLoading) {\n      console.log('Login already in progress, ignoring duplicate call');\n      return;\n    }\n    if (!this.username || !this.password) {\n      this.errorMessage = 'Vui lòng nhập đầy đủ thông tin đăng nhập';\n      return;\n    }\n    console.log('Login initiated');\n    this.isLoading = true;\n    this.errorMessage = '';\n    const credentials = {\n      username: this.username,\n      password: this.password\n    };\n    this.authService.login(credentials).subscribe({\n      next: response => {\n        this.isLoading = false;\n        console.log('Login successful:', response);\n        console.log('Token stored:', StorageUtil.getAccessToken() ? '✅ Present' : '❌ Missing');\n        console.log('Is authenticated:', this.authService.isAuthenticated());\n        // Debug storage state\n        StorageUtil.debugStorage();\n        // Navigation is handled in the service\n      },\n\n      error: error => {\n        this.isLoading = false;\n        this.errorMessage = error.error?.message || 'Đăng nhập thất bại. Vui lòng thử lại.';\n        console.error('Login error:', error);\n      }\n    });\n  }\n  // Handle Enter key press\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !this.isLoading) {\n      this.login();\n    }\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    decls: 74,\n    vars: 15,\n    consts: [[1, \"login-container\"], [1, \"left-panel\"], [1, \"brand-section\"], [1, \"brand-title\"], [1, \"brand-subtitle\"], [1, \"brand-description\"], [1, \"decorative-circle\", \"circle-1\"], [1, \"decorative-circle\", \"circle-2\"], [1, \"decorative-circle\", \"circle-3\"], [1, \"modules-section\"], [1, \"module-card\"], [1, \"module-title\"], [1, \"module-subtitle\"], [1, \"right-panel\"], [1, \"login-form-container\"], [1, \"login-header\"], [1, \"login-title\"], [1, \"login-subtitle\"], [1, \"login-form\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"form-group\"], [\"for\", \"username\", 1, \"form-label\"], [\"id\", \"username\", \"name\", \"username\", \"type\", \"text\", \"placeholder\", \"Nh\\u1EADp t\\u00EAn \\u0111\\u0103ng nh\\u1EADp c\\u1EE7a b\\u1EA1n\", \"required\", \"\", 1, \"form-input\", 3, \"ngModel\", \"disabled\", \"ngModelChange\", \"keypress\"], [\"for\", \"password\", 1, \"form-label\"], [1, \"password-wrapper\"], [\"id\", \"password\", \"name\", \"password\", \"placeholder\", \"Nh\\u1EADp m\\u1EADt kh\\u1EA9u\", \"required\", \"\", 1, \"form-input\", \"password-input\", 3, \"type\", \"ngModel\", \"disabled\", \"ngModelChange\", \"keypress\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"disabled\", \"click\"], [1, \"form-options\"], [1, \"checkbox-wrapper\"], [\"type\", \"checkbox\", \"name\", \"rememberMe\", 3, \"ngModel\", \"ngModelChange\"], [1, \"checkmark\"], [\"href\", \"#\", 1, \"forgot-password\"], [\"type\", \"submit\", 1, \"login-button\", 3, \"disabled\"], [4, \"ngIf\"], [1, \"error-message\"], [1, \"pi\", \"pi-spin\", \"pi-spinner\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n        i0.ɵɵtext(4, \"EXEX\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"h2\", 4);\n        i0.ɵɵtext(6, \"H\\u1EC7 Th\\u1ED1ng Qu\\u1EA3n L\\u00FD Doanh Nghi\\u1EC7p Chuy\\u00EAn Nghi\\u1EC7p\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 5)(8, \"p\");\n        i0.ɵɵtext(9, \"N\\u1EC1n t\\u1EA3ng qu\\u1EA3n l\\u00FD to\\u00E0n di\\u1EC7n, k\\u1EBFt n\\u1ED1i m\\u1ECDi quy tr\\u00ECnh doanh nghi\\u1EC7p,\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"p\");\n        i0.ɵɵtext(11, \"T\\u1EEB t\\u1EADp k\\u1EBF ho\\u1EA1ch s\\u1EA3n xu\\u1EA5t \\u0111\\u1EBFn b\\u00E1o c\\u00E1o ph\\u00E2n t\\u00EDch, t\\u1ED1i \\u01B0u h\\u00F3a\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\");\n        i0.ɵɵtext(13, \"hi\\u1EC7u qu\\u1EA3 v\\u1EADn h\\u00E0nh m\\u1ECDi c\\u00E1ch th\\u00F4ng minh v\\u00E0 hi\\u1EC7n \\u0111\\u1EA1i.\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(14, \"div\", 6)(15, \"div\", 7)(16, \"div\", 8);\n        i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10)(19, \"div\", 11);\n        i0.ɵɵtext(20, \"EXEX\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 12);\n        i0.ɵɵtext(22, \"Qu\\u1EA3n l\\u00FD n\\u1EE3\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"div\", 10)(24, \"div\", 11);\n        i0.ɵɵtext(25, \"ASPROVA\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"div\", 12);\n        i0.ɵɵtext(27, \"L\\u1EADp k\\u1EBF ho\\u1EA1ch s\\u1EA3n xu\\u1EA5t\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"div\", 10)(29, \"div\", 11);\n        i0.ɵɵtext(30, \"MOTION BOARD\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(31, \"div\", 12);\n        i0.ɵɵtext(32, \"B\\u00E1o c\\u00E1o BI\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 10)(34, \"div\", 11);\n        i0.ɵɵtext(35, \"IREPORTER\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"div\", 12);\n        i0.ɵɵtext(37, \"B\\u00E1o c\\u00E1o t\\u1EF1 \\u0111\\u1ED9ng\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"div\", 10)(39, \"div\", 11);\n        i0.ɵɵtext(40, \"SVF\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"div\", 12);\n        i0.ɵɵtext(42, \"In \\u1EA5n t\\u1EF1 \\u0111\\u1ED9ng\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(43, \"div\", 13)(44, \"div\", 14)(45, \"div\", 15)(46, \"h3\", 16);\n        i0.ɵɵtext(47, \"Ch\\u00E0o m\\u1EEBng tr\\u1EDF l\\u1EA1i\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(48, \"p\", 17);\n        i0.ɵɵtext(49, \"\\u0110\\u0103ng nh\\u1EADp \\u0111\\u1EC3 truy c\\u1EADp h\\u1EC7 th\\u1ED1ng\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(50, \"form\", 18, 19);\n        i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_50_listener() {\n          return ctx.login();\n        });\n        i0.ɵɵtemplate(52, LoginComponent_div_52_Template, 2, 1, \"div\", 20);\n        i0.ɵɵelementStart(53, \"div\", 21)(54, \"label\", 22);\n        i0.ɵɵtext(55, \"T\\u00EAn \\u0111\\u0103ng nh\\u1EADp\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(56, \"input\", 23);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_56_listener($event) {\n          return ctx.username = $event;\n        })(\"keypress\", function LoginComponent_Template_input_keypress_56_listener($event) {\n          return ctx.onKeyPress($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(57, \"div\", 21)(58, \"label\", 24);\n        i0.ɵɵtext(59, \"M\\u1EADt kh\\u1EA9u\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"div\", 25)(61, \"input\", 26);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_61_listener($event) {\n          return ctx.password = $event;\n        })(\"keypress\", function LoginComponent_Template_input_keypress_61_listener($event) {\n          return ctx.onKeyPress($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(62, \"button\", 27);\n        i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_62_listener() {\n          return ctx.togglePassword();\n        });\n        i0.ɵɵelement(63, \"i\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(64, \"div\", 28)(65, \"label\", 29)(66, \"input\", 30);\n        i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_66_listener($event) {\n          return ctx.rememberMe = $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(67, \"span\", 31);\n        i0.ɵɵtext(68, \" Ghi nh\\u1EDB \\u0111\\u0103ng nh\\u1EADp \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(69, \"a\", 32);\n        i0.ɵɵtext(70, \"Qu\\u00EAn m\\u1EADt kh\\u1EA9u?\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(71, \"button\", 33);\n        i0.ɵɵtemplate(72, LoginComponent_span_72_Template, 2, 0, \"span\", 34)(73, LoginComponent_span_73_Template, 3, 0, \"span\", 34);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        const _r0 = i0.ɵɵreference(51);\n        i0.ɵɵadvance(52);\n        i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngModel\", ctx.username)(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\")(\"ngModel\", ctx.password)(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵclassMap(ctx.showPassword ? \"pi pi-eye-slash\" : \"pi pi-eye\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngModel\", ctx.rememberMe);\n        i0.ɵɵadvance(5);\n        i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n        i0.ɵɵproperty(\"disabled\", ctx.isLoading || !_r0.form.valid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n      }\n    },\n    dependencies: [i3.NgIf, i4.ɵNgNoValidate, i4.DefaultValueAccessor, i4.CheckboxControlValueAccessor, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgForm],\n    styles: [\"[_nghost-%COMP%]     .pi-eye, [_nghost-%COMP%]     .pi-eye-slash {\\n  transform: scale(1.6);\\n  margin-right: 1rem;\\n  color: var(--primary-color) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY29yZS9jb21wb25lbnRzL2F1dGgvbG9naW4vbG9naW4uY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUNZOztFQUVJLHFCQUFBO0VBQ0Esa0JBQUE7RUFDQSxzQ0FBQTtBQUFoQiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgICAgICAgICAgOmhvc3QgOjpuZy1kZWVwIC5waS1leWUsXG4gICAgICAgICAgICA6aG9zdCA6Om5nLWRlZXAgLnBpLWV5ZS1zbGFzaCB7XG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjYpO1xuICAgICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMXJlbTtcbiAgICAgICAgICAgICAgICBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcikgIWltcG9ydGFudDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["StorageUtil", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "errorMessage", "ɵɵelement", "LoginComponent", "constructor", "layoutService", "authService", "val<PERSON><PERSON><PERSON>", "username", "password", "isLoading", "isAuthenticated", "console", "log", "redirectToDashboard", "login", "credentials", "subscribe", "next", "response", "getAccessToken", "debugStorage", "error", "message", "onKeyPress", "event", "key", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "AuthService", "_2", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_50_listener", "ɵɵtemplate", "LoginComponent_div_52_Template", "LoginComponent_Template_input_ngModelChange_56_listener", "$event", "LoginComponent_Template_input_keypress_56_listener", "LoginComponent_Template_input_ngModelChange_61_listener", "LoginComponent_Template_input_keypress_61_listener", "LoginComponent_Template_button_click_62_listener", "togglePassword", "LoginComponent_Template_input_ngModelChange_66_listener", "rememberMe", "LoginComponent_span_72_Template", "LoginComponent_span_73_Template", "ɵɵproperty", "showPassword", "ɵɵclassMap", "ɵɵclassProp", "_r0", "form", "valid"], "sources": ["C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.component.ts", "C:\\DATA\\Source\\Nissin_Tech\\exex_debt\\exex-debt-app\\src\\app\\core\\components\\auth\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { AuthService } from '@app/core/services/auth.service';\r\nimport { LayoutService } from 'src/app/layout/app.layout.service';\r\nimport { LoginRequest } from '@app/core/models/auth.model';\r\nimport { StorageUtil } from '@app/core/utils/storage.util';\r\n\r\n@Component({\r\n    selector: 'app-login',\r\n    templateUrl: './login.component.html',\r\n    styles: [\r\n        `\r\n            :host ::ng-deep .pi-eye,\r\n            :host ::ng-deep .pi-eye-slash {\r\n                transform: scale(1.6);\r\n                margin-right: 1rem;\r\n                color: var(--primary-color) !important;\r\n            }\r\n        `,\r\n    ],\r\n})\r\nexport class LoginComponent {\r\n    valCheck: string[] = ['remember'];\r\n    username: string = '';\r\n    password: string = '';\r\n    errorMessage: string = '';\r\n    isLoading: boolean = false;\r\n\r\n    constructor(\r\n        public layoutService: LayoutService,\r\n        private authService: AuthService,\r\n    ) {\r\n        // Only redirect if already authenticated\r\n        if (this.authService.isAuthenticated()) {\r\n            console.log('User already authenticated, redirecting to dashboard');\r\n            this.authService.redirectToDashboard();\r\n        }\r\n    }\r\n\r\n    login() {\r\n        // Prevent multiple submissions\r\n        if (this.isLoading) {\r\n            console.log('Login already in progress, ignoring duplicate call');\r\n            return;\r\n        }\r\n\r\n        if (!this.username || !this.password) {\r\n            this.errorMessage = 'Vui lòng nhập đầy đủ thông tin đăng nhập';\r\n            return;\r\n        }\r\n\r\n        console.log('Login initiated');\r\n        this.isLoading = true;\r\n        this.errorMessage = '';\r\n\r\n        const credentials: LoginRequest = {\r\n            username: this.username,\r\n            password: this.password\r\n        };\r\n\r\n        this.authService.login(credentials).subscribe({\r\n            next: (response) => {\r\n                this.isLoading = false;\r\n                console.log('Login successful:', response);\r\n                console.log('Token stored:', StorageUtil.getAccessToken() ? '✅ Present' : '❌ Missing');\r\n                console.log('Is authenticated:', this.authService.isAuthenticated());\r\n                // Debug storage state\r\n                StorageUtil.debugStorage();\r\n                // Navigation is handled in the service\r\n            },\r\n            error: (error) => {\r\n                this.isLoading = false;\r\n                this.errorMessage = error.error?.message || 'Đăng nhập thất bại. Vui lòng thử lại.';\r\n                console.error('Login error:', error);\r\n            }\r\n        });\r\n    }\r\n\r\n    // Handle Enter key press\r\n    onKeyPress(event: KeyboardEvent) {\r\n        if (event.key === 'Enter' && !this.isLoading) {\r\n            this.login();\r\n        }\r\n    }\r\n}\r\n", "<div class=\"login-container\">\r\n    <!-- Left Panel - Brand & Info -->\r\n    <div class=\"left-panel\">\r\n        <div class=\"brand-section\">\r\n            <h1 class=\"brand-title\">EXEX</h1>\r\n            <h2 class=\"brand-subtitle\">Hệ Thống Quản Lý <PERSON>h <PERSON></h2>\r\n\r\n            <div class=\"brand-description\">\r\n                <p>Nền tảng quản lý toàn diện, kết nối mọi quy trình doanh nghiệ<PERSON>,</p>\r\n                <p>Từ tập kế hoạch sản xuất đến báo cáo phân tích, tối <PERSON>u hóa</p>\r\n                <p>hiệu quả vận hành mọi cách thông minh và hiện đại.</p>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- Decorative circles -->\r\n        <div class=\"decorative-circle circle-1\"></div>\r\n        <div class=\"decorative-circle circle-2\"></div>\r\n        <div class=\"decorative-circle circle-3\"></div>\r\n\r\n        <!-- Bottom modules -->\r\n        <div class=\"modules-section\">\r\n            <div class=\"module-card\">\r\n                <div class=\"module-title\">EXEX</div>\r\n                <div class=\"module-subtitle\">Quản lý nợ</div>\r\n            </div>\r\n            <div class=\"module-card\">\r\n                <div class=\"module-title\">ASPROVA</div>\r\n                <div class=\"module-subtitle\">Lập kế hoạch sản xuất</div>\r\n            </div>\r\n            <div class=\"module-card\">\r\n                <div class=\"module-title\">MOTION BOARD</div>\r\n                <div class=\"module-subtitle\">Báo cáo BI</div>\r\n            </div>\r\n            <div class=\"module-card\">\r\n                <div class=\"module-title\">IREPORTER</div>\r\n                <div class=\"module-subtitle\">Báo cáo tự động</div>\r\n            </div>\r\n            <div class=\"module-card\">\r\n                <div class=\"module-title\">SVF</div>\r\n                <div class=\"module-subtitle\">In ấn tự động</div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <!-- Right Panel - Login Form -->\r\n    <div class=\"right-panel\">\r\n        <div class=\"login-form-container\">\r\n            <div class=\"login-header\">\r\n                <h3 class=\"login-title\">Chào mừng trở lại</h3>\r\n                <p class=\"login-subtitle\">Đăng nhập để truy cập hệ thống</p>\r\n            </div>\r\n\r\n            <form (ngSubmit)=\"login()\" #loginForm=\"ngForm\" class=\"login-form\">\r\n                <!-- Error message display -->\r\n                <div *ngIf=\"errorMessage\" class=\"error-message\">\r\n                    {{ errorMessage }}\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                    <label for=\"username\" class=\"form-label\">Tên đăng nhập</label>\r\n                    <input\r\n                        id=\"username\"\r\n                        name=\"username\"\r\n                        type=\"text\"\r\n                        placeholder=\"Nhập tên đăng nhập của bạn\"\r\n                        [(ngModel)]=\"username\"\r\n                        class=\"form-input\"\r\n                        [disabled]=\"isLoading\"\r\n                        (keypress)=\"onKeyPress($event)\"\r\n                        required />\r\n                </div>\r\n\r\n                <div class=\"form-group\">\r\n                    <label for=\"password\" class=\"form-label\">Mật khẩu</label>\r\n                    <div class=\"password-wrapper\">\r\n                        <input\r\n                            id=\"password\"\r\n                            name=\"password\"\r\n                            [type]=\"showPassword ? 'text' : 'password'\"\r\n                            placeholder=\"Nhập mật khẩu\"\r\n                            [(ngModel)]=\"password\"\r\n                            class=\"form-input password-input\"\r\n                            [disabled]=\"isLoading\"\r\n                            (keypress)=\"onKeyPress($event)\"\r\n                            required />\r\n                        <button\r\n                            type=\"button\"\r\n                            class=\"password-toggle\"\r\n                            (click)=\"togglePassword()\"\r\n                            [disabled]=\"isLoading\">\r\n                            <i [class]=\"showPassword ? 'pi pi-eye-slash' : 'pi pi-eye'\"></i>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"form-options\">\r\n                    <label class=\"checkbox-wrapper\">\r\n                        <input type=\"checkbox\" [(ngModel)]=\"rememberMe\" name=\"rememberMe\">\r\n                        <span class=\"checkmark\"></span>\r\n                        Ghi nhớ đăng nhập\r\n                    </label>\r\n                    <a href=\"#\" class=\"forgot-password\">Quên mật khẩu?</a>\r\n                </div>\r\n\r\n                <button\r\n                    type=\"submit\"\r\n                    class=\"login-button\"\r\n                    [disabled]=\"isLoading || !loginForm.form.valid\"\r\n                    [class.loading]=\"isLoading\">\r\n                    <span *ngIf=\"!isLoading\">Đăng nhập</span>\r\n                    <span *ngIf=\"isLoading\">\r\n                        <i class=\"pi pi-spin pi-spinner\"></i>\r\n                        Đang đăng nhập...\r\n                    </span>\r\n                </button>\r\n            </form>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AAIA,SAASA,WAAW,QAAQ,8BAA8B;;;;;;;;ICkD1CC,EAAA,CAAAC,cAAA,cAAgD;IAC5CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,YAAA,MACJ;;;;;IAqDIP,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,+BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACzCH,EAAA,CAAAC,cAAA,WAAwB;IACpBD,EAAA,CAAAQ,SAAA,YAAqC;IACrCR,EAAA,CAAAE,MAAA,8CACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD7F3B,OAAM,MAAOM,cAAc;EAOvBC,YACWC,aAA4B,EAC3BC,WAAwB;IADzB,KAAAD,aAAa,GAAbA,aAAa;IACZ,KAAAC,WAAW,GAAXA,WAAW;IARvB,KAAAC,QAAQ,GAAa,CAAC,UAAU,CAAC;IACjC,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAR,YAAY,GAAW,EAAE;IACzB,KAAAS,SAAS,GAAY,KAAK;IAMtB;IACA,IAAI,IAAI,CAACJ,WAAW,CAACK,eAAe,EAAE,EAAE;MACpCC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;MACnE,IAAI,CAACP,WAAW,CAACQ,mBAAmB,EAAE;;EAE9C;EAEAC,KAAKA,CAAA;IACD;IACA,IAAI,IAAI,CAACL,SAAS,EAAE;MAChBE,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE;;IAGJ,IAAI,CAAC,IAAI,CAACL,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAClC,IAAI,CAACR,YAAY,GAAG,0CAA0C;MAC9D;;IAGJW,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9B,IAAI,CAACH,SAAS,GAAG,IAAI;IACrB,IAAI,CAACT,YAAY,GAAG,EAAE;IAEtB,MAAMe,WAAW,GAAiB;MAC9BR,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,QAAQ,EAAE,IAAI,CAACA;KAClB;IAED,IAAI,CAACH,WAAW,CAACS,KAAK,CAACC,WAAW,CAAC,CAACC,SAAS,CAAC;MAC1CC,IAAI,EAAGC,QAAQ,IAAI;QACf,IAAI,CAACT,SAAS,GAAG,KAAK;QACtBE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEM,QAAQ,CAAC;QAC1CP,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEpB,WAAW,CAAC2B,cAAc,EAAE,GAAG,WAAW,GAAG,WAAW,CAAC;QACtFR,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACP,WAAW,CAACK,eAAe,EAAE,CAAC;QACpE;QACAlB,WAAW,CAAC4B,YAAY,EAAE;QAC1B;MACJ,CAAC;;MACDC,KAAK,EAAGA,KAAK,IAAI;QACb,IAAI,CAACZ,SAAS,GAAG,KAAK;QACtB,IAAI,CAACT,YAAY,GAAGqB,KAAK,CAACA,KAAK,EAAEC,OAAO,IAAI,uCAAuC;QACnFX,OAAO,CAACU,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACxC;KACH,CAAC;EACN;EAEA;EACAE,UAAUA,CAACC,KAAoB;IAC3B,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAChB,SAAS,EAAE;MAC1C,IAAI,CAACK,KAAK,EAAE;;EAEpB;EAAC,QAAAY,CAAA,G;qBA9DQxB,cAAc,EAAAT,EAAA,CAAAkC,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAApC,EAAA,CAAAkC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAd9B,cAAc;IAAA+B,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCpB3B9C,EAAA,CAAAC,cAAA,aAA6B;QAIOD,EAAA,CAAAE,MAAA,WAAI;QAAAF,EAAA,CAAAG,YAAA,EAAK;QACjCH,EAAA,CAAAC,cAAA,YAA2B;QAAAD,EAAA,CAAAE,MAAA,qFAA2C;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAE3EH,EAAA,CAAAC,cAAA,aAA+B;QACxBD,EAAA,CAAAE,MAAA,6HAA+D;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACtEH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,6IAA0D;QAAAF,EAAA,CAAAG,YAAA,EAAI;QACjEH,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,iHAAkD;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAKjEH,EAAA,CAAAQ,SAAA,cAA8C;QAK9CR,EAAA,CAAAC,cAAA,cAA6B;QAEKD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACpCH,EAAA,CAAAC,cAAA,eAA6B;QAAAD,EAAA,CAAAE,MAAA,iCAAU;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAEjDH,EAAA,CAAAC,cAAA,eAAyB;QACKD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACvCH,EAAA,CAAAC,cAAA,eAA6B;QAAAD,EAAA,CAAAE,MAAA,sDAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAE5DH,EAAA,CAAAC,cAAA,eAAyB;QACKD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAC5CH,EAAA,CAAAC,cAAA,eAA6B;QAAAD,EAAA,CAAAE,MAAA,4BAAU;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAEjDH,EAAA,CAAAC,cAAA,eAAyB;QACKD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACzCH,EAAA,CAAAC,cAAA,eAA6B;QAAAD,EAAA,CAAAE,MAAA,gDAAe;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAEtDH,EAAA,CAAAC,cAAA,eAAyB;QACKD,EAAA,CAAAE,MAAA,WAAG;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACnCH,EAAA,CAAAC,cAAA,eAA6B;QAAAD,EAAA,CAAAE,MAAA,yCAAa;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAM5DH,EAAA,CAAAC,cAAA,eAAyB;QAGWD,EAAA,CAAAE,MAAA,6CAAiB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAC9CH,EAAA,CAAAC,cAAA,aAA0B;QAAAD,EAAA,CAAAE,MAAA,8EAA8B;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGhEH,EAAA,CAAAC,cAAA,oBAAkE;QAA5DD,EAAA,CAAAgD,UAAA,sBAAAC,kDAAA;UAAA,OAAYF,GAAA,CAAA1B,KAAA,EAAO;QAAA,EAAC;QAEtBrB,EAAA,CAAAkD,UAAA,KAAAC,8BAAA,kBAEM;QAENnD,EAAA,CAAAC,cAAA,eAAwB;QACqBD,EAAA,CAAAE,MAAA,yCAAa;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QAC9DH,EAAA,CAAAC,cAAA,iBASe;QAJXD,EAAA,CAAAgD,UAAA,2BAAAI,wDAAAC,MAAA;UAAA,OAAAN,GAAA,CAAAjC,QAAA,GAAAuC,MAAA;QAAA,EAAsB,sBAAAC,mDAAAD,MAAA;UAAA,OAGVN,GAAA,CAAAjB,UAAA,CAAAuB,MAAA,CAAkB;QAAA,EAHR;QAL1BrD,EAAA,CAAAG,YAAA,EASe;QAGnBH,EAAA,CAAAC,cAAA,eAAwB;QACqBD,EAAA,CAAAE,MAAA,0BAAQ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACzDH,EAAA,CAAAC,cAAA,eAA8B;QAMtBD,EAAA,CAAAgD,UAAA,2BAAAO,wDAAAF,MAAA;UAAA,OAAAN,GAAA,CAAAhC,QAAA,GAAAsC,MAAA;QAAA,EAAsB,sBAAAG,mDAAAH,MAAA;UAAA,OAGVN,GAAA,CAAAjB,UAAA,CAAAuB,MAAA,CAAkB;QAAA,EAHR;QAL1BrD,EAAA,CAAAG,YAAA,EASe;QACfH,EAAA,CAAAC,cAAA,kBAI2B;QADvBD,EAAA,CAAAgD,UAAA,mBAAAS,iDAAA;UAAA,OAASV,GAAA,CAAAW,cAAA,EAAgB;QAAA,EAAC;QAE1B1D,EAAA,CAAAQ,SAAA,SAAgE;QACpER,EAAA,CAAAG,YAAA,EAAS;QAIjBH,EAAA,CAAAC,cAAA,eAA0B;QAEKD,EAAA,CAAAgD,UAAA,2BAAAW,wDAAAN,MAAA;UAAA,OAAAN,GAAA,CAAAa,UAAA,GAAAP,MAAA;QAAA,EAAwB;QAA/CrD,EAAA,CAAAG,YAAA,EAAkE;QAClEH,EAAA,CAAAQ,SAAA,gBAA+B;QAC/BR,EAAA,CAAAE,MAAA,+CACJ;QAAAF,EAAA,CAAAG,YAAA,EAAQ;QACRH,EAAA,CAAAC,cAAA,aAAoC;QAAAD,EAAA,CAAAE,MAAA,qCAAc;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAG1DH,EAAA,CAAAC,cAAA,kBAIgC;QAC5BD,EAAA,CAAAkD,UAAA,KAAAW,+BAAA,mBAAyC,KAAAC,+BAAA;QAK7C9D,EAAA,CAAAG,YAAA,EAAS;;;;QA5DHH,EAAA,CAAAI,SAAA,IAAkB;QAAlBJ,EAAA,CAAA+D,UAAA,SAAAhB,GAAA,CAAAxC,YAAA,CAAkB;QAWhBP,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAA+D,UAAA,YAAAhB,GAAA,CAAAjC,QAAA,CAAsB,aAAAiC,GAAA,CAAA/B,SAAA;QAalBhB,EAAA,CAAAI,SAAA,GAA2C;QAA3CJ,EAAA,CAAA+D,UAAA,SAAAhB,GAAA,CAAAiB,YAAA,uBAA2C,YAAAjB,GAAA,CAAAhC,QAAA,cAAAgC,GAAA,CAAA/B,SAAA;QAW3ChB,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAA+D,UAAA,aAAAhB,GAAA,CAAA/B,SAAA,CAAsB;QACnBhB,EAAA,CAAAI,SAAA,GAAwD;QAAxDJ,EAAA,CAAAiE,UAAA,CAAAlB,GAAA,CAAAiB,YAAA,mCAAwD;QAOxChE,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAA+D,UAAA,YAAAhB,GAAA,CAAAa,UAAA,CAAwB;QAWnD5D,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAAkE,WAAA,YAAAnB,GAAA,CAAA/B,SAAA,CAA2B;QAD3BhB,EAAA,CAAA+D,UAAA,aAAAhB,GAAA,CAAA/B,SAAA,KAAAmD,GAAA,CAAAC,IAAA,CAAAC,KAAA,CAA+C;QAExCrE,EAAA,CAAAI,SAAA,GAAgB;QAAhBJ,EAAA,CAAA+D,UAAA,UAAAhB,GAAA,CAAA/B,SAAA,CAAgB;QAChBhB,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAA+D,UAAA,SAAAhB,GAAA,CAAA/B,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}