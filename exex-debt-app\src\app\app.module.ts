import { LocationStrategy, PathLocationStrategy } from '@angular/common';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgxSpinnerModule } from 'ngx-spinner';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { NotfoundComponent } from './core/components/notfound/notfound.component';
import { AuthInterceptor } from './core/interceptors/auth.interceptor';
import { BaseUrlInterceptor } from './core/interceptors/base-url.interceptor';
import { ErrorInterceptor } from './core/interceptors/error.interceptor';
import { HeadersInterceptor } from './core/interceptors/headers.interceptor';
import { JwtRefreshInterceptor } from './core/interceptors/jwt-refresh.interceptor';
import { LoadingInterceptor } from './core/interceptors/loading.interceptor';
import { LoggingInterceptor } from './core/interceptors/logging.interceptor';
import { OfflineModeInterceptor } from './core/interceptors/offline-mode.interceptor';
import { RequestTimingInterceptor } from './core/interceptors/request-timing.interceptor';
import { RetryInterceptor } from './core/interceptors/retry.interceptor';
import { TimeoutInterceptor } from './core/interceptors/timeout.interceptor';
import { AppLayoutModule } from './layout/app.layout.module';
import { TranslocoRootModule } from './transloco-root.module';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ConfirmDialogModule } from 'primeng/confirmdialog';

@NgModule({
    declarations: [AppComponent, NotfoundComponent],
    imports: [
        AppRoutingModule,
        AppLayoutModule,
        NgxSpinnerModule,
        BrowserAnimationsModule,
        BrowserModule,
        HttpClientModule,
        TranslocoRootModule,
        ToastModule,
        ConfirmDialogModule,
    ],
    providers: [
        MessageService,
        ConfirmationService,
        { provide: LocationStrategy, useClass: PathLocationStrategy },
        // Base URL should be first to set correct URLs
        {
            provide: HTTP_INTERCEPTORS,
            useClass: BaseUrlInterceptor,
            multi: true,
        },
        // Headers should be early to set content-type
        {
            provide: HTTP_INTERCEPTORS,
            useClass: HeadersInterceptor,
            multi: true,
        },
        // Auth interceptor to add Authorization header
        { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
        // JWT refresh should come after auth interceptor
        {
            provide: HTTP_INTERCEPTORS,
            useClass: JwtRefreshInterceptor,
            multi: true,
        },
        // Error handling
        { provide: HTTP_INTERCEPTORS, useClass: ErrorInterceptor, multi: true },
        // Timeout
        {
            provide: HTTP_INTERCEPTORS,
            useClass: TimeoutInterceptor,
            multi: true,
        },
        // Retry
        { provide: HTTP_INTERCEPTORS, useClass: RetryInterceptor, multi: true },
        // Loading indicator
        {
            provide: HTTP_INTERCEPTORS,
            useClass: LoadingInterceptor,
            multi: true,
        },
        // Offline mode
        {
            provide: HTTP_INTERCEPTORS,
            useClass: OfflineModeInterceptor,
            multi: true,
        },
        // Logging should be last to see final requests
        {
            provide: HTTP_INTERCEPTORS,
            useClass: LoggingInterceptor,
            multi: true,
        },
        // Request timing
        {
            provide: HTTP_INTERCEPTORS,
            useClass: RequestTimingInterceptor,
            multi: true,
        },
    ],
    bootstrap: [AppComponent],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {}
