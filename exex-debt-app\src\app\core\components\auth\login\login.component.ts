import { Component } from '@angular/core';
import { AuthService } from '@app/core/services/auth.service';
import { LayoutService } from 'src/app/layout/app.layout.service';
import { LoginRequest } from '@app/core/models/auth.model';
import { StorageUtil } from '@app/core/utils/storage.util';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.scss']
})
export class LoginComponent {
    username: string = '';
    password: string = '';
    errorMessage: string = '';
    isLoading: boolean = false;
    showPassword: boolean = false;
    rememberMe: boolean = false;

    constructor(
        public layoutService: LayoutService,
        private authService: AuthService,
    ) {
        // Only redirect if already authenticated
        if (this.authService.isAuthenticated()) {
            console.log('User already authenticated, redirecting to dashboard');
            this.authService.redirectToDashboard();
        }
    }

    login() {
        // Prevent multiple submissions
        if (this.isLoading) {
            console.log('Login already in progress, ignoring duplicate call');
            return;
        }

        if (!this.username || !this.password) {
            this.errorMessage = 'Vui lòng nhập đầy đủ thông tin đăng nhập';
            return;
        }

        console.log('Login initiated');
        this.isLoading = true;
        this.errorMessage = '';

        const credentials: LoginRequest = {
            username: this.username,
            password: this.password
        };

        this.authService.login(credentials).subscribe({
            next: (response) => {
                this.isLoading = false;
                console.log('Login successful:', response);
                console.log('Token stored:', StorageUtil.getAccessToken() ? '✅ Present' : '❌ Missing');
                console.log('Is authenticated:', this.authService.isAuthenticated());
                // Debug storage state
                StorageUtil.debugStorage();
                // Navigation is handled in the service
            },
            error: (error) => {
                this.isLoading = false;
                this.errorMessage = error.error?.message || 'Đăng nhập thất bại. Vui lòng thử lại.';
                console.error('Login error:', error);
            }
        });
    }

    // Handle Enter key press
    onKeyPress(event: KeyboardEvent) {
        if (event.key === 'Enter' && !this.isLoading) {
            this.login();
        }
    }

    togglePassword() {
        this.showPassword = !this.showPassword;
    }
}
