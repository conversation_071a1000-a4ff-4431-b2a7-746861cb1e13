import { AuthService } from '@app/core/services/auth.service';
import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { Path } from '../enums/path.enum';
import { StorageUtil } from '../utils/storage.util';

export const authGuard: CanActivateFn = (_, state) => {
    const authService = inject(AuthService);
    const isAuth = authService.isAuthenticated();
    const redirectToLogin = inject(Router).createUrlTree([Path.AUTH_LOGIN]);

    console.log('AuthGuard check:', {
        isAuthenticated: isAuth,
        route: state.url,
        token: StorageUtil.getAccessToken() ? '✅ Present' : '❌ Missing'
    });

    return isAuth ? true : redirectToLogin;
};
