import { AuthService } from '@app/core/services/auth.service';
//ng generate interceptor my-interceptor --skip-tests

import { inject, Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, <PERSON>ttpHandler } from '@angular/common/http';
import { StorageUtil } from '../utils/storage.util';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
    authService = inject(AuthService);
    intercept(request: HttpRequest<any>, next: HttpHandler) {
        // Skip adding Authorization header for login and refresh endpoints only
        const isLoginEndpoint = request.url.includes('/api/auth/login');
        const isRefreshEndpoint = request.url.includes('/api/auth/refresh');
        const token = StorageUtil.getAccessToken();

        if (!isLoginEndpoint && !isRefreshEndpoint && token) {
            const authRequest = request.clone({
                headers: request.headers.set('Authorization', `Bearer ${token}`),
            });
            return next.handle(authRequest);
        }

        return next.handle(request);
    }
}
