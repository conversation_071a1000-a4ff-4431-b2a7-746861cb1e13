import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler } from '@angular/common/http';

@Injectable()
export class HeadersInterceptor implements HttpInterceptor {
    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>) {
        // Only add Content-Type if not already set, and preserve existing headers
        let headers = request.headers;

        if (!headers.has('Content-Type')) {
            headers = headers.set('Content-Type', 'application/json');
        }

        const headersRequest = request.clone({ headers });
        return next.handle(headersRequest);
    }
}
