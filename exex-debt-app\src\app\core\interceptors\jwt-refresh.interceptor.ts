import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';
import { catchError, switchMap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { StorageUtil } from '../utils/storage.util';
import { AuthService } from '../services/auth.service';

@Injectable()
export class JwtRefreshInterceptor implements HttpInterceptor {
    constructor(private authService: AuthService) {}

    intercept(request: HttpRequest<any>, next: HttpHandler) {
        return next.handle(request).pipe(
            catchError((error: HttpErrorResponse) => {
                // Skip refresh logic for login, refresh, and logout endpoints
                const isLoginEndpoint = request.url.includes('/api/auth/login');
                const isRefreshEndpoint = request.url.includes('/api/auth/refresh');
                const isLogoutEndpoint = request.url.includes('/api/auth/logout');

                if (error.status === 401 && !isLoginEndpoint && !isRefreshEndpoint && !isLogoutEndpoint) {
                    // Token expired or unauthorized; attempt to refresh it
                    return this.authService.refreshToken().pipe(
                        switchMap((response) => {
                            // Retry the original request with the new token
                            const updatedRequest = request.clone({
                                setHeaders: {
                                    Authorization: `Bearer ${StorageUtil.getAccessToken()}`,
                                },
                            });
                            return next.handle(updatedRequest);
                        }),
                        catchError((refreshError) => {
                            // Refresh token failed; log out the user immediately
                            this.authService.logoutImmediate();
                            return throwError(() => refreshError);
                        }),
                    );
                }
                return throwError(() => error);
            }),
        );
    }
}
