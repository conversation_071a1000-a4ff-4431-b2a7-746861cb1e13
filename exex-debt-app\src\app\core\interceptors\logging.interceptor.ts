import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpResponse } from '@angular/common/http';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements HttpInterceptor {
    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>) {
        // Log outgoing requests
        console.log(`🚀 HTTP ${request.method} Request:`, request.url);

        return next.handle(request).pipe(
            tap((event) => {
                if (event instanceof HttpResponse) {
                    console.log(`✅ HTTP ${request.method} Response (${event.status}):`, request.url);
                }
            }),
        );
    }
}
