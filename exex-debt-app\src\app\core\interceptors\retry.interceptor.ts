import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpErrorResponse } from '@angular/common/http';
import { retry, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';

@Injectable()
export class RetryInterceptor implements HttpInterceptor {
    constructor() {}

    intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler) {
        // Skip retry for auth endpoints and POST requests (to avoid duplicate submissions)
        const isAuthEndpoint = request.url.includes('/api/auth/');
        const isPostRequest = request.method === 'POST';

        // Only retry GET requests and non-auth endpoints
        if (isAuthEndpoint || isPostRequest) {
            return next.handle(request);
        }

        // Define the maximum number of retries for safe operations only
        const maxRetries = 2;
        return next.handle(request).pipe(
            retry({
                count: maxRetries,
                delay: (error: HttpErrorResponse, retryCount: number) => {
                    // Only retry on network errors or 5xx server errors
                    if (error.status >= 500 || error.status === 0) {
                        console.log(`Retrying request (${retryCount}/${maxRetries}):`, request.url);
                        return throwError(() => error);
                    }
                    // Don't retry on 4xx client errors
                    throw error;
                }
            }),
            catchError((error: HttpErrorResponse) => {
                return throwError(() => error);
            })
        );
    }
}
