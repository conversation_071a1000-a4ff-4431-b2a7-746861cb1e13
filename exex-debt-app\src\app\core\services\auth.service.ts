import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
import { Observable, tap, catchError, throwError } from 'rxjs';
import { Auth } from '../enums/auth.enum';
import { Path } from '../enums/path.enum';
import { LoginRequest, LoginResponse, LogoutResponse, RefreshTokenResponse } from '../models/auth.model';
import { StorageUtil } from '../utils/storage.util';

@Injectable({
    providedIn: 'root',
})
export class AuthService {
    private jwtToken;
    redirectTo = inject(Router);

    constructor(
        private http: HttpClient,
        private spinner: NgxSpinnerService,
    ) {
        const token = StorageUtil.getAccessToken();
        if (token) {
            this.jwtToken = token;
        }
    }

    refreshToken(): Observable<RefreshTokenResponse> {
        const refreshToken = StorageUtil.getRefreshToken();
        if (!refreshToken) {
            return throwError(() => new Error('No refresh token available'));
        }

        return this.http.post<RefreshTokenResponse>('api/auth/refresh', { refreshToken }).pipe(
            tap((response) => {
                if (response.success && response.data) {
                    StorageUtil.setTokens(response.data.accessToken, response.data.refreshToken);
                    this.jwtToken = response.data.accessToken;
                }
            }),
            catchError((error) => {
                this.logoutImmediate();
                return throwError(() => error);
            })
        );
    }

    public isAuthenticated(): boolean {
        // Check both in-memory token and localStorage
        const token = this.jwtToken || StorageUtil.getAccessToken();
        if (!token) {
            return false;
        }

        // Update in-memory token if it's missing but exists in localStorage
        if (!this.jwtToken && token) {
            this.jwtToken = token;
        }

        return true;
    }

    redirectToDashboard() {
        this.jwtToken = StorageUtil.getAccessToken();
        this.jwtToken && this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);
    }

    login(credentials: LoginRequest): Observable<LoginResponse> {
        this.spinner.show();

        return this.http.post<LoginResponse>('api/auth/login', credentials).pipe(
            tap((response) => {
                if (response.success && response.data) {
                    this.jwtToken = response.data.accessToken;
                    StorageUtil.setTokens(response.data.accessToken, response.data.refreshToken);
                    this.redirectTo.navigate([Path.DASHBOARD_CUSTOMER]);
                }
                this.spinner.hide();
            }),
            catchError((error) => {
                this.spinner.hide();
                return throwError(() => error);
            })
        );
    }

    logout(): Observable<LogoutResponse> {
        console.log('Logout initiated');

        return this.http.post<LogoutResponse>('api/auth/logout', {}).pipe(
            tap((response) => {
                console.log('Logout API success:', response);
                // Clear tokens regardless of API response
                this.clearTokensAndRedirect();
            }),
            catchError((error) => {
                console.log('Logout API failed:', error);
                // Clear tokens even if logout API fails
                this.clearTokensAndRedirect();
                // Don't throw error, just complete successfully
                return new Observable<LogoutResponse>(subscriber => {
                    subscriber.next({
                        success: true,
                        message: 'Đăng xuất thành công (local)',
                        data: 'Logged out locally'
                    });
                    subscriber.complete();
                });
            })
        );
    }

    private clearTokensAndRedirect(): void {
        console.log('Clearing tokens and redirecting');
        this.jwtToken = null;
        StorageUtil.clearAuthTokens();
        this.redirectTo.navigate([Path.AUTH_LOGIN]);
    }

    // Method for immediate logout without API call (for error scenarios)
    logoutImmediate(): void {
        console.log('Immediate logout');
        this.clearTokensAndRedirect();
    }

    // Method to clear all app data (for complete reset)
    clearAllAppData(): void {
        console.log('Clearing all app data');
        this.jwtToken = null;
        StorageUtil.clearAllAppData();
        this.redirectTo.navigate([Path.AUTH_LOGIN]);
    }

    mockUser(): Observable<any> {
        return this.http.get('https://jsonplaceholder.typicode.com/users');
    }
}
