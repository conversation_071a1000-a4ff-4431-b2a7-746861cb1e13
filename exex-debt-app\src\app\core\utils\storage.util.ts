import { Auth } from '../enums/auth.enum';

/**
 * Utility class for managing localStorage with app-specific keys
 * This helps avoid conflicts with other applications
 */
export class StorageUtil {
    // Authentication related storage keys
    static readonly AUTH_KEYS = Auth;

    // App-specific storage keys for other data
    static readonly APP_KEYS = {
        THEME: 'debt_theme',
        COLOR_SCHEME: 'debt_color_scheme',
        SCALE: 'debt_scale',
        LANGUAGE: 'debt_language'
    } as const;

    /**
     * Get authentication token from storage
     */
    static getAccessToken(): string | null {
        return localStorage.getItem(Auth.ACCESS_TOKEN);
    }

    /**
     * Get refresh token from storage
     */
    static getRefreshToken(): string | null {
        return localStorage.getItem(Auth.REFRESH_TOKEN);
    }

    /**
     * Set authentication tokens
     */
    static setTokens(accessToken: string, refreshToken: string): void {
        localStorage.setItem(Auth.ACCESS_TOKEN, accessToken);
        localStorage.setItem(Auth.REFRESH_TOKEN, refreshToken);
    }

    /**
     * Clear all authentication tokens
     */
    static clearAuthTokens(): void {
        localStorage.removeItem(Auth.ACCESS_TOKEN);
        localStorage.removeItem(Auth.REFRESH_TOKEN);
    }

    /**
     * Check if user has valid tokens
     */
    static hasValidTokens(): boolean {
        return !!(this.getAccessToken() && this.getRefreshToken());
    }

    /**
     * Get app setting from storage
     */
    static getAppSetting(key: keyof typeof StorageUtil.APP_KEYS): string | null {
        return localStorage.getItem(StorageUtil.APP_KEYS[key]);
    }

    /**
     * Set app setting to storage
     */
    static setAppSetting(key: keyof typeof StorageUtil.APP_KEYS, value: string): void {
        localStorage.setItem(StorageUtil.APP_KEYS[key], value);
    }

    /**
     * Clear all app-specific data from storage
     */
    static clearAllAppData(): void {
        // Clear auth tokens
        this.clearAuthTokens();
        
        // Clear app settings
        Object.values(StorageUtil.APP_KEYS).forEach(key => {
            localStorage.removeItem(key);
        });
    }

    /**
     * Debug method to log all app-related storage keys
     */
    static debugStorage(): void {
        console.group('🔍 Debt App Storage Debug');
        console.log('Access Token:', this.getAccessToken() ? '✅ Present' : '❌ Missing');
        console.log('Refresh Token:', this.getRefreshToken() ? '✅ Present' : '❌ Missing');
        
        Object.entries(StorageUtil.APP_KEYS).forEach(([name, key]) => {
            const value = localStorage.getItem(key);
            console.log(`${name}:`, value || '❌ Not set');
        });
        console.groupEnd();
    }
}
