import { Path } from './../core/enums/path.enum';
import { Component, ElementRef, ViewChild } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { LayoutService } from './app.layout.service';
import { AuthService } from '@app/core/services/auth.service';

@Component({
    selector: 'app-topbar',
    template: `<div class="layout-topbar justify-content-between">
        <div class="flex align-items-center">
            <!-- <button #menubutton class="p-link layout-topbar-button" (click)="layoutService.onMenuToggle()">
                <i class="pi pi-bars"></i>
            </button> -->
            <p-image
                #menubutton
                src="assets/images/exex.png"
                alt="Image"
                width="100"
                class="p-link"
                (click)="layoutService.onMenuToggle()" />
        </div>
        <div>
            <p-avatar
                [routerLink]="pathUser"
                class="custom-avatar mr-4 p-link"
                icon="pi pi-user"
                image="assets/user-avatar.png"
                shape="circle"
                size="large"></p-avatar>

            <i class="pi pi-bell mr-3 text-2xl p-link" pBadge badgeSize="small" severity="danger" value="2"></i>

            <button class="p-link layout-topbar-button" (click)="signout()" title="Đăng xuất">
                <i class="pi pi-sign-out text-2xl"></i>
            </button>
        </div>
    </div>`,
})
export class AppTopBarComponent {
    pathUser = Path.DASHBOARD_USER;

    items!: MenuItem[];

    @ViewChild('menubutton') menuButton!: ElementRef;

    @ViewChild('topbarmenubutton') topbarMenuButton!: ElementRef;

    @ViewChild('topbarmenu') menu!: ElementRef;

    constructor(
        public layoutService: LayoutService,
        private authService: AuthService,
    ) {}

    signout() {
        console.log('Signout button clicked');

        // Try API logout first, but fallback to immediate logout if needed
        this.authService.logout().subscribe({
            next: (response) => {
                console.log('Logout successful:', response.message);
            },
            error: (error) => {
                console.error('Logout error, falling back to immediate logout:', error);
                // Fallback to immediate logout if API fails
                this.authService.logoutImmediate();
            }
        });
    }

    // Emergency logout method (can be called from console if needed)
    forceLogout() {
        console.log('Force logout initiated');
        this.authService.logoutImmediate();
    }

    // Complete app data reset (can be called from console if needed)
    resetAppData() {
        console.log('App data reset initiated');
        this.authService.clearAllAppData();
    }
}
